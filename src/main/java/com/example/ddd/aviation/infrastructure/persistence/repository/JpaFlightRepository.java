package com.example.ddd.aviation.infrastructure.persistence.repository;

import com.example.ddd.aviation.infrastructure.persistence.entity.FlightEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA Repository for FlightEntity.
 * 
 * This interface extends JpaRepository to provide database access for Flight entities.
 * It includes custom query methods for business-specific flight searches and filtering.
 * 
 * Key Design Decisions:
 * - Uses Spring Data JPA for automatic implementation
 * - Provides custom queries for complex business scenarios
 * - Optimized queries with proper indexing considerations
 * - Returns entities that will be converted to domain objects
 * - Includes performance-optimized queries for common use cases
 * 
 * <AUTHOR> Aviation System
 */
@Repository
public interface JpaFlightRepository extends JpaRepository<FlightEntity, String> {
    
    /**
     * Finds a flight by flight number and departure date.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.flightNumber = :flightNumber " +
           "AND DATE(f.departureTime) = :departureDate")
    Optional<FlightEntity> findByFlightNumberAndDepartureDate(
        @Param("flightNumber") String flightNumber, 
        @Param("departureDate") LocalDate departureDate);
    
    /**
     * Finds flights by route and departure date.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.origin = :origin " +
           "AND f.destination = :destination AND DATE(f.departureTime) = :departureDate " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findByRouteAndDepartureDate(
        @Param("origin") String origin, 
        @Param("destination") String destination, 
        @Param("departureDate") LocalDate departureDate);
    
    /**
     * Finds flights by origin, destination, and date range.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.origin = :origin " +
           "AND f.destination = :destination " +
           "AND DATE(f.departureTime) BETWEEN :startDate AND :endDate " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findByOriginAndDestinationAndDepartureDateBetween(
        @Param("origin") String origin,
        @Param("destination") String destination,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate);
    
    /**
     * Finds all bookable flights (scheduled status with available seats).
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.status = 'SCHEDULED' " +
           "AND f.bookedSeats < f.totalCapacity " +
           "AND f.departureTime > CURRENT_TIMESTAMP " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findBookableFlights();
    
    /**
     * Finds bookable flights for a specific route and date.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.origin = :origin " +
           "AND f.destination = :destination " +
           "AND DATE(f.departureTime) = :departureDate " +
           "AND f.status = 'SCHEDULED' " +
           "AND f.bookedSeats < f.totalCapacity " +
           "AND f.departureTime > CURRENT_TIMESTAMP " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findBookableFlightsByRouteAndDate(
        @Param("origin") String origin,
        @Param("destination") String destination,
        @Param("departureDate") LocalDate departureDate);
    
    /**
     * Finds flights with high demand (>80% occupancy).
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "(CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) > 0.8 " +
           "ORDER BY (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) DESC")
    List<FlightEntity> findHighDemandFlights();
    
    /**
     * Finds flights with low demand (<30% occupancy).
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "(CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) < 0.3 " +
           "ORDER BY (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) ASC")
    List<FlightEntity> findLowDemandFlights();
    
    /**
     * Finds flights departing within specified hours.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.departureTime BETWEEN CURRENT_TIMESTAMP " +
           "AND DATEADD('HOUR', :hours, CURRENT_TIMESTAMP) " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findFlightsDepartingWithinHours(@Param("hours") int hours);
    
    /**
     * Finds flights departing after specified days.
     */
    @Query("SELECT f FROM FlightEntity f WHERE f.departureTime > " +
           "DATEADD('DAY', :days, CURRENT_TIMESTAMP) " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findFlightsDepartingAfterDays(@Param("days") int days);
    
    /**
     * Finds flights by aircraft type.
     */
    List<FlightEntity> findByAircraftTypeOrderByDepartureTime(String aircraftType);
    
    /**
     * Finds domestic flights (simplified logic for demo).
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "(f.origin LIKE 'K%' OR f.origin IN ('LAX','JFK','ORD','DFW','DEN','SFO','SEA','LAS','PHX','IAH','CLT','MIA','BOS','MSP','DTW','PHL','LGA','BWI','SLC','DCA')) " +
           "AND (f.destination LIKE 'K%' OR f.destination IN ('LAX','JFK','ORD','DFW','DEN','SFO','SEA','LAS','PHX','IAH','CLT','MIA','BOS','MSP','DTW','PHL','LGA','BWI','SLC','DCA')) " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findDomesticFlights();
    
    /**
     * Finds international flights.
     */
    @Query("SELECT f FROM FlightEntity f WHERE NOT (" +
           "(f.origin LIKE 'K%' OR f.origin IN ('LAX','JFK','ORD','DFW','DEN','SFO','SEA','LAS','PHX','IAH','CLT','MIA','BOS','MSP','DTW','PHL','LGA','BWI','SLC','DCA')) " +
           "AND (f.destination LIKE 'K%' OR f.destination IN ('LAX','JFK','ORD','DFW','DEN','SFO','SEA','LAS','PHX','IAH','CLT','MIA','BOS','MSP','DTW','PHL','LGA','BWI','SLC','DCA'))" +
           ") ORDER BY f.departureTime")
    List<FlightEntity> findInternationalFlights();
    
    /**
     * Finds flights by status.
     */
    List<FlightEntity> findByStatusOrderByDepartureTime(FlightEntity.FlightStatusEntity status);
    
    /**
     * Finds flights with available seats greater than minimum.
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "(f.totalCapacity - f.bookedSeats) >= :minimumSeats " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findFlightsWithAvailableSeats(@Param("minimumSeats") int minimumSeats);
    
    /**
     * Finds flights departing between specific times.
     */
    List<FlightEntity> findByDepartureTimeBetweenOrderByDepartureTime(
        LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Finds flights by season (based on departure month).
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "CASE " +
           "  WHEN MONTH(f.departureTime) IN (12, 1, 2) THEN 'WINTER' " +
           "  WHEN MONTH(f.departureTime) IN (3, 4, 5) THEN 'SPRING' " +
           "  WHEN MONTH(f.departureTime) IN (6, 7, 8) THEN 'SUMMER' " +
           "  ELSE 'FALL' " +
           "END = :season " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findBySeason(@Param("season") String season);
    
    /**
     * Finds flights by time of day.
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "CASE " +
           "  WHEN HOUR(f.departureTime) BETWEEN 6 AND 11 THEN 'MORNING' " +
           "  WHEN HOUR(f.departureTime) BETWEEN 12 AND 17 THEN 'AFTERNOON' " +
           "  WHEN HOUR(f.departureTime) BETWEEN 18 AND 20 THEN 'EVENING' " +
           "  ELSE 'RED_EYE' " +
           "END = :timeOfDay " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findByTimeOfDay(@Param("timeOfDay") String timeOfDay);
    
    /**
     * Finds flights by demand level.
     */
    @Query("SELECT f FROM FlightEntity f WHERE " +
           "CASE " +
           "  WHEN (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) >= 0.9 THEN 'VERY_HIGH' " +
           "  WHEN (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) >= 0.7 THEN 'HIGH' " +
           "  WHEN (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) >= 0.4 THEN 'MEDIUM' " +
           "  WHEN (CAST(f.bookedSeats AS double) / CAST(f.totalCapacity AS double)) >= 0.2 THEN 'LOW' " +
           "  ELSE 'VERY_LOW' " +
           "END = :demandLevel " +
           "ORDER BY f.departureTime")
    List<FlightEntity> findByDemandLevel(@Param("demandLevel") String demandLevel);
    
    /**
     * Counts flights by status.
     */
    long countByStatus(FlightEntity.FlightStatusEntity status);
    
    /**
     * Counts bookable flights for route and date.
     */
    @Query("SELECT COUNT(f) FROM FlightEntity f WHERE f.origin = :origin " +
           "AND f.destination = :destination " +
           "AND DATE(f.departureTime) = :departureDate " +
           "AND f.status = 'SCHEDULED' " +
           "AND f.bookedSeats < f.totalCapacity " +
           "AND f.departureTime > CURRENT_TIMESTAMP")
    long countBookableFlightsByRouteAndDate(
        @Param("origin") String origin,
        @Param("destination") String destination,
        @Param("departureDate") LocalDate departureDate);
    
    /**
     * Checks if flight exists by flight number and departure date.
     */
    @Query("SELECT CASE WHEN COUNT(f) > 0 THEN true ELSE false END " +
           "FROM FlightEntity f WHERE f.flightNumber = :flightNumber " +
           "AND DATE(f.departureTime) = :departureDate")
    boolean existsByFlightNumberAndDepartureDate(
        @Param("flightNumber") String flightNumber,
        @Param("departureDate") LocalDate departureDate);
}
