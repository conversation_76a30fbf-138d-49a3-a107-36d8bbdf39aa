package com.example.ddd.aviation.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;

/**
 * JPA Entity for Flight persistence.
 * 
 * This entity represents the database mapping for Flight domain objects.
 * It follows the pattern of separating persistence concerns from domain logic
 * by providing a pure data structure that can be converted to/from domain objects.
 * 
 * Key Design Decisions:
 * - Separate entity from domain model to maintain clean architecture
 * - Uses primitive types and simple objects for JPA compatibility
 * - Includes database-specific annotations and constraints
 * - Provides conversion methods to/from domain objects
 * - Uses Lombok to reduce boilerplate code
 * 
 * <AUTHOR> Aviation System
 */
@Entity
@Table(name = "flights", indexes = {
    @Index(name = "idx_flight_number_departure", columnList = "flightNumber, departureTime"),
    @Index(name = "idx_route", columnList = "origin, destination"),
    @Index(name = "idx_departure_time", columnList = "departureTime"),
    @Index(name = "idx_status", columnList = "status")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightEntity {
    
    @Id
    @Column(name = "flight_id", length = 36)
    private String flightId;
    
    @Column(name = "flight_number", nullable = false, length = 10)
    private String flightNumber;
    
    @Column(name = "origin", nullable = false, length = 3)
    private String origin;
    
    @Column(name = "destination", nullable = false, length = 3)
    private String destination;
    
    @Column(name = "departure_time", nullable = false)
    private LocalDateTime departureTime;
    
    @Column(name = "arrival_time", nullable = false)
    private LocalDateTime arrivalTime;
    
    @Column(name = "aircraft_type", nullable = false, length = 50)
    private String aircraftType;
    
    @Column(name = "total_capacity", nullable = false)
    private Integer totalCapacity;
    
    @Column(name = "booked_seats", nullable = false)
    private Integer bookedSeats;
    
    @Column(name = "base_price_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal basePriceAmount;
    
    @Column(name = "base_price_currency", nullable = false, length = 3)
    private String basePriceCurrency;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private FlightStatusEntity status;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Enumeration for flight status in persistence layer.
     */
    public enum FlightStatusEntity {
        SCHEDULED, BOARDING, DEPARTED, ARRIVED, CANCELLED, DELAYED
    }
    
    /**
     * Converts this entity to a domain Flight object.
     * 
     * @return Flight domain object
     */
    public com.example.ddd.aviation.domain.model.Flight toDomain() {
        // Create value objects
        var route = com.example.ddd.aviation.domain.model.FlightRoute.of(origin, destination);
        var schedule = com.example.ddd.aviation.domain.model.FlightSchedule.of(departureTime, arrivalTime);
        var money = new com.example.ddd.aviation.domain.model.Money(
            basePriceAmount, Currency.getInstance(basePriceCurrency));
        
        // Create domain flight
        var flight = new com.example.ddd.aviation.domain.model.Flight(
            flightId, flightNumber, route, schedule, aircraftType, totalCapacity, money);
        
        // Set additional state
        if (bookedSeats > 0) {
            flight.bookSeats(bookedSeats);
        }
        
        flight.updateStatus(convertStatusToDomain(status));
        
        return flight;
    }
    
    /**
     * Creates an entity from a domain Flight object.
     * 
     * @param flight Domain flight object
     * @return FlightEntity
     */
    public static FlightEntity fromDomain(com.example.ddd.aviation.domain.model.Flight flight) {
        return FlightEntity.builder()
            .flightId(flight.getFlightId())
            .flightNumber(flight.getFlightNumber())
            .origin(flight.getRoute().origin())
            .destination(flight.getRoute().destination())
            .departureTime(flight.getSchedule().departureTime())
            .arrivalTime(flight.getSchedule().arrivalTime())
            .aircraftType(flight.getAircraftType())
            .totalCapacity(flight.getTotalCapacity())
            .bookedSeats(flight.getBookedSeats())
            .basePriceAmount(flight.getBasePrice().amount())
            .basePriceCurrency(flight.getBasePrice().currency().getCurrencyCode())
            .status(convertStatusFromDomain(flight.getStatus()))
            .createdAt(flight.getCreatedAt())
            .updatedAt(flight.getUpdatedAt())
            .build();
    }
    
    /**
     * Converts entity status to domain status.
     */
    private com.example.ddd.aviation.domain.model.Flight.FlightStatus convertStatusToDomain(FlightStatusEntity entityStatus) {
        return switch (entityStatus) {
            case SCHEDULED -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.SCHEDULED;
            case BOARDING -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.BOARDING;
            case DEPARTED -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.DEPARTED;
            case ARRIVED -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.ARRIVED;
            case CANCELLED -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.CANCELLED;
            case DELAYED -> com.example.ddd.aviation.domain.model.Flight.FlightStatus.DELAYED;
        };
    }
    
    /**
     * Converts domain status to entity status.
     */
    private static FlightStatusEntity convertStatusFromDomain(com.example.ddd.aviation.domain.model.Flight.FlightStatus domainStatus) {
        return switch (domainStatus) {
            case SCHEDULED -> FlightStatusEntity.SCHEDULED;
            case BOARDING -> FlightStatusEntity.BOARDING;
            case DEPARTED -> FlightStatusEntity.DEPARTED;
            case ARRIVED -> FlightStatusEntity.ARRIVED;
            case CANCELLED -> FlightStatusEntity.CANCELLED;
            case DELAYED -> FlightStatusEntity.DELAYED;
        };
    }
    
    /**
     * Pre-persist callback to set timestamps.
     */
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Pre-update callback to update timestamp.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
