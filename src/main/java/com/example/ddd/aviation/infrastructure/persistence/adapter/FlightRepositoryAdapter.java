package com.example.ddd.aviation.infrastructure.persistence.adapter;

import com.example.ddd.aviation.domain.model.Flight;
import com.example.ddd.aviation.domain.model.FlightRoute;
import com.example.ddd.aviation.domain.model.FlightSchedule;
import com.example.ddd.aviation.domain.repository.FlightRepository;
import com.example.ddd.aviation.infrastructure.persistence.entity.FlightEntity;
import com.example.ddd.aviation.infrastructure.persistence.repository.JpaFlightRepository;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter that implements the domain FlightRepository interface using JPA.
 * 
 * This adapter serves as a bridge between the domain layer and the infrastructure layer,
 * converting between domain objects and JPA entities. It implements the repository
 * interface defined in the domain layer using Spring Data JPA.
 * 
 * Key Design Decisions:
 * - Implements domain repository interface to maintain dependency inversion
 * - Converts between domain objects and JPA entities
 * - Delegates to Spring Data JPA repository for actual persistence
 * - Handles all necessary conversions and mappings
 * - Provides clean separation between domain and infrastructure concerns
 * 
 * <AUTHOR> Aviation System
 */
@Component
public class FlightRepositoryAdapter implements FlightRepository {
    
    private final JpaFlightRepository jpaRepository;
    
    public FlightRepositoryAdapter(JpaFlightRepository jpaRepository) {
        this.jpaRepository = jpaRepository;
    }
    
    @Override
    public Flight save(Flight flight) {
        FlightEntity entity = FlightEntity.fromDomain(flight);
        FlightEntity savedEntity = jpaRepository.save(entity);
        return savedEntity.toDomain();
    }
    
    @Override
    public Optional<Flight> findById(String flightId) {
        return jpaRepository.findById(flightId)
            .map(FlightEntity::toDomain);
    }
    
    @Override
    public Optional<Flight> findByFlightNumberAndDepartureDate(String flightNumber, LocalDate departureDate) {
        return jpaRepository.findByFlightNumberAndDepartureDate(flightNumber, departureDate)
            .map(FlightEntity::toDomain);
    }
    
    @Override
    public List<Flight> findByRouteAndDepartureDate(FlightRoute route, LocalDate departureDate) {
        return jpaRepository.findByRouteAndDepartureDate(route.origin(), route.destination(), departureDate)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByOriginAndDestinationAndDepartureDateBetween(
            String origin, String destination, LocalDate startDate, LocalDate endDate) {
        return jpaRepository.findByOriginAndDestinationAndDepartureDateBetween(
                origin, destination, startDate, endDate)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findBookableFlights() {
        return jpaRepository.findBookableFlights()
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findBookableFlightsByRouteAndDate(FlightRoute route, LocalDate departureDate) {
        return jpaRepository.findBookableFlightsByRouteAndDate(
                route.origin(), route.destination(), departureDate)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findHighDemandFlights() {
        return jpaRepository.findHighDemandFlights()
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findLowDemandFlights() {
        return jpaRepository.findLowDemandFlights()
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findFlightsDepartingWithinHours(int hours) {
        return jpaRepository.findFlightsDepartingWithinHours(hours)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findFlightsDepartingAfterDays(int days) {
        return jpaRepository.findFlightsDepartingAfterDays(days)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByAircraftType(String aircraftType) {
        return jpaRepository.findByAircraftTypeOrderByDepartureTime(aircraftType)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findDomesticFlights() {
        return jpaRepository.findDomesticFlights()
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findInternationalFlights() {
        return jpaRepository.findInternationalFlights()
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByStatus(Flight.FlightStatus status) {
        FlightEntity.FlightStatusEntity entityStatus = convertStatusToEntity(status);
        return jpaRepository.findByStatusOrderByDepartureTime(entityStatus)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findFlightsWithAvailableSeats(int minimumSeats) {
        return jpaRepository.findFlightsWithAvailableSeats(minimumSeats)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByDepartureTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return jpaRepository.findByDepartureTimeBetweenOrderByDepartureTime(startTime, endTime)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findBySeason(FlightSchedule.Season season) {
        String seasonString = season.name();
        return jpaRepository.findBySeason(seasonString)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByTimeOfDay(FlightSchedule.TimeOfDay timeOfDay) {
        String timeOfDayString = timeOfDay.name();
        return jpaRepository.findByTimeOfDay(timeOfDayString)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Flight> findByDemandLevel(Flight.DemandLevel demandLevel) {
        String demandLevelString = demandLevel.name();
        return jpaRepository.findByDemandLevel(demandLevelString)
            .stream()
            .map(FlightEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
    
    @Override
    public long countByStatus(Flight.FlightStatus status) {
        FlightEntity.FlightStatusEntity entityStatus = convertStatusToEntity(status);
        return jpaRepository.countByStatus(entityStatus);
    }
    
    @Override
    public long countBookableFlightsByRouteAndDate(FlightRoute route, LocalDate departureDate) {
        return jpaRepository.countBookableFlightsByRouteAndDate(
            route.origin(), route.destination(), departureDate);
    }
    
    @Override
    public boolean existsByFlightNumberAndDepartureDate(String flightNumber, LocalDate departureDate) {
        return jpaRepository.existsByFlightNumberAndDepartureDate(flightNumber, departureDate);
    }
    
    @Override
    public void deleteById(String flightId) {
        jpaRepository.deleteById(flightId);
    }
    
    @Override
    public void deleteAll() {
        jpaRepository.deleteAll();
    }
    
    /**
     * Converts domain flight status to entity status.
     */
    private FlightEntity.FlightStatusEntity convertStatusToEntity(Flight.FlightStatus domainStatus) {
        return switch (domainStatus) {
            case SCHEDULED -> FlightEntity.FlightStatusEntity.SCHEDULED;
            case BOARDING -> FlightEntity.FlightStatusEntity.BOARDING;
            case DEPARTED -> FlightEntity.FlightStatusEntity.DEPARTED;
            case ARRIVED -> FlightEntity.FlightStatusEntity.ARRIVED;
            case CANCELLED -> FlightEntity.FlightStatusEntity.CANCELLED;
            case DELAYED -> FlightEntity.FlightStatusEntity.DELAYED;
        };
    }
}
