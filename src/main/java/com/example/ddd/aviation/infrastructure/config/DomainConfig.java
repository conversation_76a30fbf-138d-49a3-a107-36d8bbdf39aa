package com.example.ddd.aviation.infrastructure.config;

import com.example.ddd.aviation.domain.repository.FlightRepository;
import com.example.ddd.aviation.domain.repository.PricingRuleRepository;
import com.example.ddd.aviation.domain.service.PricingService;
import com.example.ddd.aviation.domain.service.RuleEvaluationService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for domain services and their dependencies.
 * 
 * This configuration class creates and wires domain services with their
 * required dependencies. It ensures proper dependency injection for
 * domain services while maintaining clean architecture principles.
 * 
 * Key Design Decisions:
 * - Explicitly configures domain services as Spring beans
 * - Maintains dependency inversion by injecting repository interfaces
 * - Separates domain service configuration from infrastructure concerns
 * - Provides clear wiring of domain layer dependencies
 * 
 * <AUTHOR> Aviation System
 */
@Configuration
public class DomainConfig {
    
    /**
     * Creates the RuleEvaluationService bean.
     * 
     * @param pricingRuleRepository Repository for accessing pricing rules
     * @return Configured RuleEvaluationService
     */
    @Bean
    public RuleEvaluationService ruleEvaluationService(PricingRuleRepository pricingRuleRepository) {
        return new RuleEvaluationService(pricingRuleRepository);
    }
    
    /**
     * Creates the PricingService bean.
     * 
     * @param ruleEvaluationService Service for evaluating pricing rules
     * @return Configured PricingService
     */
    @Bean
    public PricingService pricingService(RuleEvaluationService ruleEvaluationService) {
        return new PricingService(ruleEvaluationService);
    }
}
