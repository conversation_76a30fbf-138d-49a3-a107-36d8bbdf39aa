package com.example.ddd.aviation.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity for PricingRule persistence.
 * 
 * This entity represents the database mapping for PricingRule domain objects.
 * It stores rule conditions as JSON or separate fields for better queryability.
 * 
 * Key Design Decisions:
 * - Separate entity from domain model for clean architecture
 * - Stores rule conditions as individual fields for better SQL queries
 * - Uses enums for type safety in database
 * - Includes database-specific optimizations and indexes
 * - Provides conversion methods to/from domain objects
 * 
 * <AUTHOR> Aviation System
 */
@Entity
@Table(name = "pricing_rules", indexes = {
    @Index(name = "idx_rule_type", columnList = "ruleType"),
    @Index(name = "idx_active", columnList = "isActive"),
    @Index(name = "idx_priority", columnList = "priority"),
    @Index(name = "idx_rule_name", columnList = "ruleName", unique = true)
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingRuleEntity {
    
    @Id
    @Column(name = "rule_id", length = 36)
    private String ruleId;
    
    @Column(name = "rule_name", nullable = false, length = 100, unique = true)
    private String ruleName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "rule_type", nullable = false, length = 30)
    private RuleTypeEntity ruleType;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "discount_percentage", nullable = false, precision = 5, scale = 4)
    private BigDecimal discountPercentage;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
    
    @Column(name = "priority", nullable = false)
    private Integer priority;
    
    // Rule condition fields for better queryability
    @Enumerated(EnumType.STRING)
    @Column(name = "target_season", length = 20)
    private SeasonEntity targetSeason;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "minimum_loyalty_level", length = 20)
    private LoyaltyLevelEntity minimumLoyaltyLevel;
    
    @Column(name = "minimum_days_in_advance")
    private Integer minimumDaysInAdvance;
    
    @Column(name = "maximum_hours_before_departure")
    private Integer maximumHoursBeforeDeparture;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "target_demand_level", length = 20)
    private DemandLevelEntity targetDemandLevel;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "target_route_distance", length = 20)
    private RouteDistanceEntity targetRouteDistance;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "target_time_of_day", length = 20)
    private TimeOfDayEntity targetTimeOfDay;
    
    @Column(name = "requires_frequent_flyer")
    private Boolean requiresFrequentFlyer;
    
    @Column(name = "domestic_only")
    private Boolean domesticOnly;
    
    @Column(name = "international_only")
    private Boolean internationalOnly;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Enumeration for rule types in persistence layer.
     */
    public enum RuleTypeEntity {
        SEASONAL_DISCOUNT, LOYALTY_DISCOUNT, EARLY_BIRD_DISCOUNT, LAST_MINUTE_DEAL, DYNAMIC_PRICING
    }
    
    /**
     * Enumeration for seasons in persistence layer.
     */
    public enum SeasonEntity {
        SPRING, SUMMER, FALL, WINTER
    }
    
    /**
     * Enumeration for loyalty levels in persistence layer.
     */
    public enum LoyaltyLevelEntity {
        BRONZE, SILVER, GOLD, PLATINUM, DIAMOND
    }
    
    /**
     * Enumeration for demand levels in persistence layer.
     */
    public enum DemandLevelEntity {
        VERY_LOW, LOW, MEDIUM, HIGH, VERY_HIGH
    }
    
    /**
     * Enumeration for route distances in persistence layer.
     */
    public enum RouteDistanceEntity {
        SHORT_HAUL, MEDIUM_HAUL, LONG_HAUL
    }
    
    /**
     * Enumeration for time of day in persistence layer.
     */
    public enum TimeOfDayEntity {
        MORNING, AFTERNOON, EVENING, RED_EYE
    }
    
    /**
     * Converts this entity to a domain PricingRule object.
     * 
     * @return PricingRule domain object
     */
    public com.example.ddd.aviation.domain.model.PricingRule toDomain() {
        // Build rule conditions
        var conditionsBuilder = buildRuleConditions();
        
        // Create domain pricing rule
        var domainRule = new com.example.ddd.aviation.domain.model.PricingRule(
            ruleId,
            ruleName,
            convertRuleTypeToDomain(ruleType),
            description,
            discountPercentage,
            conditionsBuilder,
            priority
        );
        
        // Set activation status
        if (!isActive) {
            domainRule.deactivate();
        }
        
        return domainRule;
    }
    
    /**
     * Creates an entity from a domain PricingRule object.
     * 
     * @param rule Domain pricing rule object
     * @return PricingRuleEntity
     */
    public static PricingRuleEntity fromDomain(com.example.ddd.aviation.domain.model.PricingRule rule) {
        var builder = PricingRuleEntity.builder()
            .ruleId(rule.getRuleId())
            .ruleName(rule.getRuleName())
            .ruleType(convertRuleTypeFromDomain(rule.getRuleType()))
            .description(rule.getDescription())
            .discountPercentage(rule.getDiscountPercentage())
            .isActive(rule.isActive())
            .priority(rule.getPriority())
            .createdAt(rule.getCreatedAt())
            .updatedAt(rule.getUpdatedAt());
        
        // Extract condition fields
        var conditions = rule.getConditions();
        
        conditions.targetSeason().ifPresent(season -> 
            builder.targetSeason(convertSeasonFromDomain(season)));
        
        conditions.minimumLoyaltyLevel().ifPresent(level -> 
            builder.minimumLoyaltyLevel(convertLoyaltyLevelFromDomain(level)));
        
        conditions.minimumDaysInAdvance().ifPresent(builder::minimumDaysInAdvance);
        
        conditions.maximumHoursBeforeDeparture().ifPresent(builder::maximumHoursBeforeDeparture);
        
        conditions.targetDemandLevel().ifPresent(level -> 
            builder.targetDemandLevel(convertDemandLevelFromDomain(level)));
        
        conditions.targetRouteDistance().ifPresent(distance -> 
            builder.targetRouteDistance(convertRouteDistanceFromDomain(distance)));
        
        conditions.targetTimeOfDay().ifPresent(timeOfDay -> 
            builder.targetTimeOfDay(convertTimeOfDayFromDomain(timeOfDay)));
        
        conditions.requiresFrequentFlyer().ifPresent(builder::requiresFrequentFlyer);
        conditions.domesticOnly().ifPresent(builder::domesticOnly);
        conditions.internationalOnly().ifPresent(builder::internationalOnly);
        
        return builder.build();
    }
    
    /**
     * Builds rule conditions from entity fields.
     */
    private com.example.ddd.aviation.domain.model.RuleConditions buildRuleConditions() {
        return new com.example.ddd.aviation.domain.model.RuleConditions(
            targetSeason != null ? java.util.Optional.of(convertSeasonToDomain(targetSeason)) : java.util.Optional.empty(),
            minimumLoyaltyLevel != null ? java.util.Optional.of(convertLoyaltyLevelToDomain(minimumLoyaltyLevel)) : java.util.Optional.empty(),
            minimumDaysInAdvance != null ? java.util.Optional.of(minimumDaysInAdvance) : java.util.Optional.empty(),
            maximumHoursBeforeDeparture != null ? java.util.Optional.of(maximumHoursBeforeDeparture) : java.util.Optional.empty(),
            targetDemandLevel != null ? java.util.Optional.of(convertDemandLevelToDomain(targetDemandLevel)) : java.util.Optional.empty(),
            targetRouteDistance != null ? java.util.Optional.of(convertRouteDistanceToDomain(targetRouteDistance)) : java.util.Optional.empty(),
            targetTimeOfDay != null ? java.util.Optional.of(convertTimeOfDayToDomain(targetTimeOfDay)) : java.util.Optional.empty(),
            requiresFrequentFlyer != null ? java.util.Optional.of(requiresFrequentFlyer) : java.util.Optional.empty(),
            domesticOnly != null ? java.util.Optional.of(domesticOnly) : java.util.Optional.empty(),
            internationalOnly != null ? java.util.Optional.of(internationalOnly) : java.util.Optional.empty()
        );
    }
    
    // Conversion methods for enums
    private com.example.ddd.aviation.domain.model.PricingRule.RuleType convertRuleTypeToDomain(RuleTypeEntity entityType) {
        return switch (entityType) {
            case SEASONAL_DISCOUNT -> com.example.ddd.aviation.domain.model.PricingRule.RuleType.SEASONAL_DISCOUNT;
            case LOYALTY_DISCOUNT -> com.example.ddd.aviation.domain.model.PricingRule.RuleType.LOYALTY_DISCOUNT;
            case EARLY_BIRD_DISCOUNT -> com.example.ddd.aviation.domain.model.PricingRule.RuleType.EARLY_BIRD_DISCOUNT;
            case LAST_MINUTE_DEAL -> com.example.ddd.aviation.domain.model.PricingRule.RuleType.LAST_MINUTE_DEAL;
            case DYNAMIC_PRICING -> com.example.ddd.aviation.domain.model.PricingRule.RuleType.DYNAMIC_PRICING;
        };
    }
    
    private static RuleTypeEntity convertRuleTypeFromDomain(com.example.ddd.aviation.domain.model.PricingRule.RuleType domainType) {
        return switch (domainType) {
            case SEASONAL_DISCOUNT -> RuleTypeEntity.SEASONAL_DISCOUNT;
            case LOYALTY_DISCOUNT -> RuleTypeEntity.LOYALTY_DISCOUNT;
            case EARLY_BIRD_DISCOUNT -> RuleTypeEntity.EARLY_BIRD_DISCOUNT;
            case LAST_MINUTE_DEAL -> RuleTypeEntity.LAST_MINUTE_DEAL;
            case DYNAMIC_PRICING -> RuleTypeEntity.DYNAMIC_PRICING;
        };
    }
    
    private com.example.ddd.aviation.domain.model.FlightSchedule.Season convertSeasonToDomain(SeasonEntity entitySeason) {
        return switch (entitySeason) {
            case SPRING -> com.example.ddd.aviation.domain.model.FlightSchedule.Season.SPRING;
            case SUMMER -> com.example.ddd.aviation.domain.model.FlightSchedule.Season.SUMMER;
            case FALL -> com.example.ddd.aviation.domain.model.FlightSchedule.Season.FALL;
            case WINTER -> com.example.ddd.aviation.domain.model.FlightSchedule.Season.WINTER;
        };
    }
    
    private static SeasonEntity convertSeasonFromDomain(com.example.ddd.aviation.domain.model.FlightSchedule.Season domainSeason) {
        return switch (domainSeason) {
            case SPRING -> SeasonEntity.SPRING;
            case SUMMER -> SeasonEntity.SUMMER;
            case FALL -> SeasonEntity.FALL;
            case WINTER -> SeasonEntity.WINTER;
        };
    }
    
    private com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel convertLoyaltyLevelToDomain(LoyaltyLevelEntity entityLevel) {
        return switch (entityLevel) {
            case BRONZE -> com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.BRONZE;
            case SILVER -> com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.SILVER;
            case GOLD -> com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.GOLD;
            case PLATINUM -> com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.PLATINUM;
            case DIAMOND -> com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.DIAMOND;
        };
    }
    
    private static LoyaltyLevelEntity convertLoyaltyLevelFromDomain(com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel domainLevel) {
        return switch (domainLevel) {
            case BRONZE -> LoyaltyLevelEntity.BRONZE;
            case SILVER -> LoyaltyLevelEntity.SILVER;
            case GOLD -> LoyaltyLevelEntity.GOLD;
            case PLATINUM -> LoyaltyLevelEntity.PLATINUM;
            case DIAMOND -> LoyaltyLevelEntity.DIAMOND;
        };
    }
    
    private com.example.ddd.aviation.domain.model.Flight.DemandLevel convertDemandLevelToDomain(DemandLevelEntity entityLevel) {
        return switch (entityLevel) {
            case VERY_LOW -> com.example.ddd.aviation.domain.model.Flight.DemandLevel.VERY_LOW;
            case LOW -> com.example.ddd.aviation.domain.model.Flight.DemandLevel.LOW;
            case MEDIUM -> com.example.ddd.aviation.domain.model.Flight.DemandLevel.MEDIUM;
            case HIGH -> com.example.ddd.aviation.domain.model.Flight.DemandLevel.HIGH;
            case VERY_HIGH -> com.example.ddd.aviation.domain.model.Flight.DemandLevel.VERY_HIGH;
        };
    }
    
    private static DemandLevelEntity convertDemandLevelFromDomain(com.example.ddd.aviation.domain.model.Flight.DemandLevel domainLevel) {
        return switch (domainLevel) {
            case VERY_LOW -> DemandLevelEntity.VERY_LOW;
            case LOW -> DemandLevelEntity.LOW;
            case MEDIUM -> DemandLevelEntity.MEDIUM;
            case HIGH -> DemandLevelEntity.HIGH;
            case VERY_HIGH -> DemandLevelEntity.VERY_HIGH;
        };
    }
    
    private com.example.ddd.aviation.domain.model.FlightRoute.RouteDistance convertRouteDistanceToDomain(RouteDistanceEntity entityDistance) {
        return switch (entityDistance) {
            case SHORT_HAUL -> com.example.ddd.aviation.domain.model.FlightRoute.RouteDistance.SHORT_HAUL;
            case MEDIUM_HAUL -> com.example.ddd.aviation.domain.model.FlightRoute.RouteDistance.MEDIUM_HAUL;
            case LONG_HAUL -> com.example.ddd.aviation.domain.model.FlightRoute.RouteDistance.LONG_HAUL;
        };
    }
    
    private static RouteDistanceEntity convertRouteDistanceFromDomain(com.example.ddd.aviation.domain.model.FlightRoute.RouteDistance domainDistance) {
        return switch (domainDistance) {
            case SHORT_HAUL -> RouteDistanceEntity.SHORT_HAUL;
            case MEDIUM_HAUL -> RouteDistanceEntity.MEDIUM_HAUL;
            case LONG_HAUL -> RouteDistanceEntity.LONG_HAUL;
        };
    }
    
    private com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay convertTimeOfDayToDomain(TimeOfDayEntity entityTimeOfDay) {
        return switch (entityTimeOfDay) {
            case MORNING -> com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay.MORNING;
            case AFTERNOON -> com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay.AFTERNOON;
            case EVENING -> com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay.EVENING;
            case RED_EYE -> com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay.RED_EYE;
        };
    }
    
    private static TimeOfDayEntity convertTimeOfDayFromDomain(com.example.ddd.aviation.domain.model.FlightSchedule.TimeOfDay domainTimeOfDay) {
        return switch (domainTimeOfDay) {
            case MORNING -> TimeOfDayEntity.MORNING;
            case AFTERNOON -> TimeOfDayEntity.AFTERNOON;
            case EVENING -> TimeOfDayEntity.EVENING;
            case RED_EYE -> TimeOfDayEntity.RED_EYE;
        };
    }
    
    /**
     * Pre-persist callback to set timestamps.
     */
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Pre-update callback to update timestamp.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
