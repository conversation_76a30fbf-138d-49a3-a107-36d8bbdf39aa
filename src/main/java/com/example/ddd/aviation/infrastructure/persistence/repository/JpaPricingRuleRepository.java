package com.example.ddd.aviation.infrastructure.persistence.repository;

import com.example.ddd.aviation.infrastructure.persistence.entity.PricingRuleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA Repository for PricingRuleEntity.
 * 
 * This interface extends JpaRepository to provide database access for PricingRule entities.
 * It includes custom query methods for business-specific rule searches and filtering.
 * 
 * Key Design Decisions:
 * - Uses Spring Data JPA for automatic implementation
 * - Provides custom queries for complex rule filtering
 * - Optimized queries for pricing calculation performance
 * - Returns entities that will be converted to domain objects
 * - Includes business-focused query methods
 * 
 * <AUTHOR> Aviation System
 */
@Repository
public interface JpaPricingRuleRepository extends JpaRepository<PricingRuleEntity, String> {
    
    /**
     * Finds a pricing rule by its name.
     */
    Optional<PricingRuleEntity> findByRuleName(String ruleName);
    
    /**
     * Finds all active pricing rules.
     */
    List<PricingRuleEntity> findByIsActiveTrueOrderByPriorityDesc();
    
    /**
     * Finds all inactive pricing rules.
     */
    List<PricingRuleEntity> findByIsActiveFalseOrderByRuleName();
    
    /**
     * Finds all pricing rules ordered by priority.
     */
    List<PricingRuleEntity> findAllByOrderByPriorityDesc();
    
    /**
     * Finds pricing rules by rule type.
     */
    List<PricingRuleEntity> findByRuleTypeOrderByPriorityDesc(PricingRuleEntity.RuleTypeEntity ruleType);
    
    /**
     * Finds active pricing rules by rule type.
     */
    List<PricingRuleEntity> findByRuleTypeAndIsActiveTrueOrderByPriorityDesc(
        PricingRuleEntity.RuleTypeEntity ruleType);
    
    /**
     * Finds pricing rules that target a specific loyalty level.
     */
    List<PricingRuleEntity> findByMinimumLoyaltyLevelOrderByPriorityDesc(
        PricingRuleEntity.LoyaltyLevelEntity loyaltyLevel);
    
    /**
     * Finds pricing rules that apply to a specific season.
     */
    List<PricingRuleEntity> findByTargetSeasonOrderByPriorityDesc(
        PricingRuleEntity.SeasonEntity season);
    
    /**
     * Finds pricing rules that apply to a specific demand level.
     */
    List<PricingRuleEntity> findByTargetDemandLevelOrderByPriorityDesc(
        PricingRuleEntity.DemandLevelEntity demandLevel);
    
    /**
     * Finds pricing rules with priority greater than or equal to specified value.
     */
    List<PricingRuleEntity> findByPriorityGreaterThanEqualOrderByPriorityDesc(int minimumPriority);
    
    /**
     * Finds early bird discount rules with minimum days greater than or equal to specified value.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.ruleType = 'EARLY_BIRD_DISCOUNT' " +
           "AND r.minimumDaysInAdvance >= :minimumDays " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findEarlyBirdRulesWithMinimumDays(@Param("minimumDays") int minimumDays);
    
    /**
     * Finds last-minute deal rules with maximum hours less than or equal to specified value.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.ruleType = 'LAST_MINUTE_DEAL' " +
           "AND r.maximumHoursBeforeDeparture <= :maximumHours " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findLastMinuteRulesWithMaximumHours(@Param("maximumHours") int maximumHours);
    
    /**
     * Finds rules that require frequent flyer status.
     */
    List<PricingRuleEntity> findByRequiresFrequentFlyerTrueAndIsActiveTrueOrderByPriorityDesc();
    
    /**
     * Finds rules that apply only to domestic flights.
     */
    List<PricingRuleEntity> findByDomesticOnlyTrueAndIsActiveTrueOrderByPriorityDesc();
    
    /**
     * Finds rules that apply only to international flights.
     */
    List<PricingRuleEntity> findByInternationalOnlyTrueAndIsActiveTrueOrderByPriorityDesc();
    
    /**
     * Finds rules with discount percentage greater than or equal to specified value.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.discountPercentage >= :minimumDiscount " +
           "ORDER BY r.discountPercentage DESC, r.priority DESC")
    List<PricingRuleEntity> findByDiscountPercentageGreaterThanEqual(
        @Param("minimumDiscount") double minimumDiscount);
    
    /**
     * Finds the top N rules by discount percentage.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.isActive = true " +
           "ORDER BY r.discountPercentage DESC, r.priority DESC")
    List<PricingRuleEntity> findTopDiscountRules(@Param("limit") int limit);
    
    /**
     * Finds rules created after the specified date.
     */
    List<PricingRuleEntity> findByCreatedAtAfterOrderByCreatedAtDesc(LocalDateTime date);
    
    /**
     * Finds rules updated after the specified date.
     */
    List<PricingRuleEntity> findByUpdatedAtAfterOrderByUpdatedAtDesc(LocalDateTime date);
    
    /**
     * Finds active seasonal discount rules for a specific season.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.ruleType = 'SEASONAL_DISCOUNT' " +
           "AND r.targetSeason = :season " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findActiveSeasonalRulesForSeason(
        @Param("season") PricingRuleEntity.SeasonEntity season);
    
    /**
     * Finds active loyalty discount rules for a minimum loyalty level.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.ruleType = 'LOYALTY_DISCOUNT' " +
           "AND r.minimumLoyaltyLevel <= :loyaltyLevel " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findActiveLoyaltyRulesForLevel(
        @Param("loyaltyLevel") PricingRuleEntity.LoyaltyLevelEntity loyaltyLevel);
    
    /**
     * Finds active dynamic pricing rules for a specific demand level.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE r.ruleType = 'DYNAMIC_PRICING' " +
           "AND r.targetDemandLevel = :demandLevel " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findActiveDynamicPricingRulesForDemand(
        @Param("demandLevel") PricingRuleEntity.DemandLevelEntity demandLevel);
    
    /**
     * Finds rules applicable to a specific route distance.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE " +
           "(r.targetRouteDistance = :routeDistance OR r.targetRouteDistance IS NULL) " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findRulesForRouteDistance(
        @Param("routeDistance") PricingRuleEntity.RouteDistanceEntity routeDistance);
    
    /**
     * Finds rules applicable to a specific time of day.
     */
    @Query("SELECT r FROM PricingRuleEntity r WHERE " +
           "(r.targetTimeOfDay = :timeOfDay OR r.targetTimeOfDay IS NULL) " +
           "AND r.isActive = true " +
           "ORDER BY r.priority DESC")
    List<PricingRuleEntity> findRulesForTimeOfDay(
        @Param("timeOfDay") PricingRuleEntity.TimeOfDayEntity timeOfDay);
    
    /**
     * Counts the total number of pricing rules.
     */
    long count();
    
    /**
     * Counts active pricing rules.
     */
    long countByIsActiveTrue();
    
    /**
     * Counts pricing rules by type.
     */
    long countByRuleType(PricingRuleEntity.RuleTypeEntity ruleType);
    
    /**
     * Counts active pricing rules by type.
     */
    long countByRuleTypeAndIsActiveTrue(PricingRuleEntity.RuleTypeEntity ruleType);
    
    /**
     * Checks if an active rule exists with the given name.
     */
    boolean existsByRuleNameAndIsActiveTrue(String ruleName);
    
    /**
     * Checks if any active loyalty rules exist for the specified level.
     */
    @Query("SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END " +
           "FROM PricingRuleEntity r WHERE r.ruleType = 'LOYALTY_DISCOUNT' " +
           "AND r.minimumLoyaltyLevel <= :loyaltyLevel " +
           "AND r.isActive = true")
    boolean existsActiveLoyaltyRulesForLevel(
        @Param("loyaltyLevel") PricingRuleEntity.LoyaltyLevelEntity loyaltyLevel);
    
    /**
     * Checks if any active seasonal rules exist for the specified season.
     */
    @Query("SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END " +
           "FROM PricingRuleEntity r WHERE r.ruleType = 'SEASONAL_DISCOUNT' " +
           "AND r.targetSeason = :season " +
           "AND r.isActive = true")
    boolean existsActiveSeasonalRulesForSeason(
        @Param("season") PricingRuleEntity.SeasonEntity season);
    
    /**
     * Deletes all inactive pricing rules.
     */
    void deleteByIsActiveFalse();
}
