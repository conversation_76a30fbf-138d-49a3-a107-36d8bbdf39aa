package com.example.ddd.aviation.infrastructure.persistence.adapter;

import com.example.ddd.aviation.domain.model.PricingRule;
import com.example.ddd.aviation.domain.model.CustomerInfo;
import com.example.ddd.aviation.domain.model.FlightSchedule;
import com.example.ddd.aviation.domain.model.Flight;
import com.example.ddd.aviation.domain.repository.PricingRuleRepository;
import com.example.ddd.aviation.infrastructure.persistence.entity.PricingRuleEntity;
import com.example.ddd.aviation.infrastructure.persistence.repository.JpaPricingRuleRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter that implements the domain PricingRuleRepository interface using JPA.
 * 
 * This adapter serves as a bridge between the domain layer and the infrastructure layer,
 * converting between domain objects and JPA entities for pricing rules. It implements
 * the repository interface defined in the domain layer using Spring Data JPA.
 * 
 * Key Design Decisions:
 * - Implements domain repository interface to maintain dependency inversion
 * - Converts between domain objects and JPA entities
 * - Delegates to Spring Data JPA repository for actual persistence
 * - Handles enum conversions between domain and entity layers
 * - Provides clean separation between domain and infrastructure concerns
 * 
 * <AUTHOR> Aviation System
 */
@Component
public class PricingRuleRepositoryAdapter implements PricingRuleRepository {
    
    private final JpaPricingRuleRepository jpaRepository;
    
    public PricingRuleRepositoryAdapter(JpaPricingRuleRepository jpaRepository) {
        this.jpaRepository = jpaRepository;
    }
    
    @Override
    public PricingRule save(PricingRule pricingRule) {
        PricingRuleEntity entity = PricingRuleEntity.fromDomain(pricingRule);
        PricingRuleEntity savedEntity = jpaRepository.save(entity);
        return savedEntity.toDomain();
    }
    
    @Override
    public Optional<PricingRule> findById(String ruleId) {
        return jpaRepository.findById(ruleId)
            .map(PricingRuleEntity::toDomain);
    }
    
    @Override
    public Optional<PricingRule> findByRuleName(String ruleName) {
        return jpaRepository.findByRuleName(ruleName)
            .map(PricingRuleEntity::toDomain);
    }
    
    @Override
    public List<PricingRule> findActiveRules() {
        return jpaRepository.findByIsActiveTrueOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findInactiveRules() {
        return jpaRepository.findByIsActiveFalseOrderByRuleName()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findAll() {
        return jpaRepository.findAllByOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByRuleType(PricingRule.RuleType ruleType) {
        PricingRuleEntity.RuleTypeEntity entityType = convertRuleTypeToEntity(ruleType);
        return jpaRepository.findByRuleTypeOrderByPriorityDesc(entityType)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findActiveRulesByType(PricingRule.RuleType ruleType) {
        PricingRuleEntity.RuleTypeEntity entityType = convertRuleTypeToEntity(ruleType);
        return jpaRepository.findByRuleTypeAndIsActiveTrueOrderByPriorityDesc(entityType)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByTargetLoyaltyLevel(CustomerInfo.LoyaltyLevel loyaltyLevel) {
        PricingRuleEntity.LoyaltyLevelEntity entityLevel = convertLoyaltyLevelToEntity(loyaltyLevel);
        return jpaRepository.findByMinimumLoyaltyLevelOrderByPriorityDesc(entityLevel)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByTargetSeason(FlightSchedule.Season season) {
        PricingRuleEntity.SeasonEntity entitySeason = convertSeasonToEntity(season);
        return jpaRepository.findByTargetSeasonOrderByPriorityDesc(entitySeason)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByTargetDemandLevel(Flight.DemandLevel demandLevel) {
        PricingRuleEntity.DemandLevelEntity entityLevel = convertDemandLevelToEntity(demandLevel);
        return jpaRepository.findByTargetDemandLevelOrderByPriorityDesc(entityLevel)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByPriorityGreaterThanEqual(int minimumPriority) {
        return jpaRepository.findByPriorityGreaterThanEqualOrderByPriorityDesc(minimumPriority)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findAllOrderByPriorityDesc() {
        return jpaRepository.findAllByOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findActiveRulesOrderByPriorityDesc() {
        return jpaRepository.findByIsActiveTrueOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findEarlyBirdRulesWithMinimumDays(int minimumDays) {
        return jpaRepository.findEarlyBirdRulesWithMinimumDays(minimumDays)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findLastMinuteRulesWithMaximumHours(int maximumHours) {
        return jpaRepository.findLastMinuteRulesWithMaximumHours(maximumHours)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findRulesRequiringFrequentFlyer() {
        return jpaRepository.findByRequiresFrequentFlyerTrueAndIsActiveTrueOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findDomesticOnlyRules() {
        return jpaRepository.findByDomesticOnlyTrueAndIsActiveTrueOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findInternationalOnlyRules() {
        return jpaRepository.findByInternationalOnlyTrueAndIsActiveTrueOrderByPriorityDesc()
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findByDiscountPercentageGreaterThanEqual(double minimumDiscount) {
        return jpaRepository.findByDiscountPercentageGreaterThanEqual(minimumDiscount)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findTopDiscountRules(int limit) {
        return jpaRepository.findTopDiscountRules(limit)
            .stream()
            .limit(limit)
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findRulesCreatedAfter(LocalDateTime date) {
        return jpaRepository.findByCreatedAtAfterOrderByCreatedAtDesc(date)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<PricingRule> findRulesUpdatedAfter(LocalDateTime date) {
        return jpaRepository.findByUpdatedAtAfterOrderByUpdatedAtDesc(date)
            .stream()
            .map(PricingRuleEntity::toDomain)
            .collect(Collectors.toList());
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
    
    @Override
    public long countActiveRules() {
        return jpaRepository.countByIsActiveTrue();
    }
    
    @Override
    public long countByRuleType(PricingRule.RuleType ruleType) {
        PricingRuleEntity.RuleTypeEntity entityType = convertRuleTypeToEntity(ruleType);
        return jpaRepository.countByRuleType(entityType);
    }
    
    @Override
    public long countActiveRulesByType(PricingRule.RuleType ruleType) {
        PricingRuleEntity.RuleTypeEntity entityType = convertRuleTypeToEntity(ruleType);
        return jpaRepository.countByRuleTypeAndIsActiveTrue(entityType);
    }
    
    @Override
    public boolean existsActiveRuleByName(String ruleName) {
        return jpaRepository.existsByRuleNameAndIsActiveTrue(ruleName);
    }
    
    @Override
    public boolean existsActiveLoyaltyRulesForLevel(CustomerInfo.LoyaltyLevel loyaltyLevel) {
        PricingRuleEntity.LoyaltyLevelEntity entityLevel = convertLoyaltyLevelToEntity(loyaltyLevel);
        return jpaRepository.existsActiveLoyaltyRulesForLevel(entityLevel);
    }
    
    @Override
    public boolean existsActiveSeasonalRulesForSeason(FlightSchedule.Season season) {
        PricingRuleEntity.SeasonEntity entitySeason = convertSeasonToEntity(season);
        return jpaRepository.existsActiveSeasonalRulesForSeason(entitySeason);
    }
    
    @Override
    public void deleteById(String ruleId) {
        jpaRepository.deleteById(ruleId);
    }
    
    @Override
    public void deleteInactiveRules() {
        jpaRepository.deleteByIsActiveFalse();
    }
    
    @Override
    public void deleteAll() {
        jpaRepository.deleteAll();
    }
    
    // Conversion methods for enums
    private PricingRuleEntity.RuleTypeEntity convertRuleTypeToEntity(PricingRule.RuleType domainType) {
        return switch (domainType) {
            case SEASONAL_DISCOUNT -> PricingRuleEntity.RuleTypeEntity.SEASONAL_DISCOUNT;
            case LOYALTY_DISCOUNT -> PricingRuleEntity.RuleTypeEntity.LOYALTY_DISCOUNT;
            case EARLY_BIRD_DISCOUNT -> PricingRuleEntity.RuleTypeEntity.EARLY_BIRD_DISCOUNT;
            case LAST_MINUTE_DEAL -> PricingRuleEntity.RuleTypeEntity.LAST_MINUTE_DEAL;
            case DYNAMIC_PRICING -> PricingRuleEntity.RuleTypeEntity.DYNAMIC_PRICING;
        };
    }
    
    private PricingRuleEntity.LoyaltyLevelEntity convertLoyaltyLevelToEntity(CustomerInfo.LoyaltyLevel domainLevel) {
        return switch (domainLevel) {
            case BRONZE -> PricingRuleEntity.LoyaltyLevelEntity.BRONZE;
            case SILVER -> PricingRuleEntity.LoyaltyLevelEntity.SILVER;
            case GOLD -> PricingRuleEntity.LoyaltyLevelEntity.GOLD;
            case PLATINUM -> PricingRuleEntity.LoyaltyLevelEntity.PLATINUM;
            case DIAMOND -> PricingRuleEntity.LoyaltyLevelEntity.DIAMOND;
        };
    }
    
    private PricingRuleEntity.SeasonEntity convertSeasonToEntity(FlightSchedule.Season domainSeason) {
        return switch (domainSeason) {
            case SPRING -> PricingRuleEntity.SeasonEntity.SPRING;
            case SUMMER -> PricingRuleEntity.SeasonEntity.SUMMER;
            case FALL -> PricingRuleEntity.SeasonEntity.FALL;
            case WINTER -> PricingRuleEntity.SeasonEntity.WINTER;
        };
    }
    
    private PricingRuleEntity.DemandLevelEntity convertDemandLevelToEntity(Flight.DemandLevel domainLevel) {
        return switch (domainLevel) {
            case VERY_LOW -> PricingRuleEntity.DemandLevelEntity.VERY_LOW;
            case LOW -> PricingRuleEntity.DemandLevelEntity.LOW;
            case MEDIUM -> PricingRuleEntity.DemandLevelEntity.MEDIUM;
            case HIGH -> PricingRuleEntity.DemandLevelEntity.HIGH;
            case VERY_HIGH -> PricingRuleEntity.DemandLevelEntity.VERY_HIGH;
        };
    }
}
