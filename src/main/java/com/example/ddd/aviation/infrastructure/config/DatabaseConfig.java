package com.example.ddd.aviation.infrastructure.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Database configuration for the Aviation DDD application.
 * 
 * This configuration class sets up JPA repositories and entity scanning
 * for the aviation domain. It enables transaction management and configures
 * the package scanning for JPA entities and repositories.
 * 
 * Key Design Decisions:
 * - Separates infrastructure configuration from domain logic
 * - Enables JPA repositories with proper package scanning
 * - Configures transaction management for data consistency
 * - Uses explicit package scanning for better control
 * 
 * <AUTHOR> Aviation System
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.example.ddd.aviation.infrastructure.persistence.repository")
@EntityScan(basePackages = "com.example.ddd.aviation.infrastructure.persistence.entity")
@EnableTransactionManagement
public class DatabaseConfig {
    
    // Configuration is handled through annotations and application.properties
    // Additional beans can be defined here if needed for custom database setup
}
