package com.example.ddd.aviation.infrastructure.data;

import com.example.ddd.aviation.domain.model.*;
import com.example.ddd.aviation.domain.repository.FlightRepository;
import com.example.ddd.aviation.domain.repository.PricingRuleRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.Currency;

/**
 * Data initializer for populating the database with sample data.
 * 
 * This component runs at application startup and creates sample flights
 * and pricing rules to demonstrate the aviation pricing system. It provides
 * realistic data for testing and demonstration purposes.
 * 
 * Key Design Decisions:
 * - Runs at startup to ensure data is available for testing
 * - Creates realistic aviation data with various scenarios
 * - Demonstrates all types of pricing rules
 * - Uses domain objects to ensure business rule validation
 * - Transactional to ensure data consistency
 * 
 * <AUTHOR> Aviation System
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    private final FlightRepository flightRepository;
    private final PricingRuleRepository pricingRuleRepository;
    
    public DataInitializer(FlightRepository flightRepository, PricingRuleRepository pricingRuleRepository) {
        this.flightRepository = flightRepository;
        this.pricingRuleRepository = pricingRuleRepository;
    }
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        System.out.println("Initializing aviation pricing system data...");
        
        createSampleFlights();
        createSamplePricingRules();
        
        System.out.println("Data initialization completed successfully!");
        System.out.println("Total flights: " + flightRepository.count());
        System.out.println("Total pricing rules: " + pricingRuleRepository.count());
    }
    
    /**
     * Creates sample flights with various routes, schedules, and characteristics.
     */
    private void createSampleFlights() {
        Currency usd = Currency.getInstance("USD");
        LocalDateTime now = LocalDateTime.now();
        
        // Domestic flights
        createFlight("AA123", "LAX", "JFK", now.plusDays(1).withHour(8).withMinute(0), 
                    Duration.ofHours(5), "Boeing 737", 180, Money.usd(299.99));
        
        createFlight("DL456", "JFK", "LAX", now.plusDays(1).withHour(14).withMinute(30), 
                    Duration.ofHours(6), "Airbus A320", 150, Money.usd(349.99));
        
        createFlight("UA789", "ORD", "SFO", now.plusDays(2).withHour(7).withMinute(15), 
                    Duration.ofHours(4), "Boeing 777", 300, Money.usd(279.99));
        
        createFlight("SW101", "DEN", "PHX", now.plusDays(3).withHour(19).withMinute(45), 
                    Duration.ofHours(2), "Boeing 737", 175, Money.usd(159.99));
        
        // International flights
        createFlight("BA201", "JFK", "LHR", now.plusDays(5).withHour(22).withMinute(0), 
                    Duration.ofHours(7), "Boeing 787", 250, Money.usd(899.99));
        
        createFlight("LH301", "LAX", "FRA", now.plusDays(7).withHour(11).withMinute(30), 
                    Duration.ofHours(11), "Airbus A380", 400, Money.usd(1299.99));
        
        createFlight("AF401", "JFK", "CDG", now.plusDays(10).withHour(23).withMinute(15), 
                    Duration.ofHours(8), "Boeing 777", 280, Money.usd(799.99));
        
        // Early bird flights (far in advance)
        createFlight("AA999", "LAX", "JFK", now.plusDays(120).withHour(6).withMinute(0), 
                    Duration.ofHours(5), "Boeing 737", 180, Money.usd(399.99));
        
        // Last-minute flights (within 24 hours)
        createFlight("DL888", "JFK", "MIA", now.plusHours(18), 
                    Duration.ofHours(3), "Airbus A320", 150, Money.usd(199.99));
        
        // High-demand flight (simulate high booking)
        Flight highDemandFlight = createFlight("UA555", "SFO", "LAX", now.plusDays(4).withHour(17).withMinute(0), 
                                              Duration.ofHours(1), "Boeing 737", 180, Money.usd(189.99));
        highDemandFlight.bookSeats(160); // 89% occupancy
        flightRepository.save(highDemandFlight);
        
        System.out.println("Created sample flights");
    }
    
    /**
     * Creates sample pricing rules covering all business scenarios.
     */
    private void createSamplePricingRules() {
        // Seasonal discounts
        pricingRuleRepository.save(PricingRule.createSeasonalDiscount(
            "Summer Travel Discount", FlightSchedule.Season.SUMMER, BigDecimal.valueOf(0.15)));
        
        pricingRuleRepository.save(PricingRule.createSeasonalDiscount(
            "Winter Holiday Special", FlightSchedule.Season.WINTER, BigDecimal.valueOf(0.10)));
        
        // Loyalty discounts
        pricingRuleRepository.save(PricingRule.createLoyaltyDiscount(
            "Silver Member Discount", CustomerInfo.LoyaltyLevel.SILVER, BigDecimal.valueOf(0.05)));
        
        pricingRuleRepository.save(PricingRule.createLoyaltyDiscount(
            "Gold Member Discount", CustomerInfo.LoyaltyLevel.GOLD, BigDecimal.valueOf(0.10)));
        
        pricingRuleRepository.save(PricingRule.createLoyaltyDiscount(
            "Platinum Member Discount", CustomerInfo.LoyaltyLevel.PLATINUM, BigDecimal.valueOf(0.15)));
        
        pricingRuleRepository.save(PricingRule.createLoyaltyDiscount(
            "Diamond Member Discount", CustomerInfo.LoyaltyLevel.DIAMOND, BigDecimal.valueOf(0.20)));
        
        // Early bird discounts
        pricingRuleRepository.save(PricingRule.createEarlyBirdDiscount(
            "Super Early Bird", 90, BigDecimal.valueOf(0.25)));
        
        pricingRuleRepository.save(PricingRule.createEarlyBirdDiscount(
            "Early Bird Special", 30, BigDecimal.valueOf(0.15)));
        
        // Last-minute deals
        pricingRuleRepository.save(PricingRule.createLastMinuteDeal(
            "Last Minute Deal", 24, BigDecimal.valueOf(0.20)));
        
        pricingRuleRepository.save(PricingRule.createLastMinuteDeal(
            "Same Day Special", 6, BigDecimal.valueOf(0.30)));
        
        // Dynamic pricing rules
        pricingRuleRepository.save(PricingRule.createDynamicPricing(
            "High Demand Surcharge", Flight.DemandLevel.VERY_HIGH, BigDecimal.valueOf(0.35)));
        
        pricingRuleRepository.save(PricingRule.createDynamicPricing(
            "Medium Demand Adjustment", Flight.DemandLevel.HIGH, BigDecimal.valueOf(0.20)));
        
        pricingRuleRepository.save(PricingRule.createDynamicPricing(
            "Low Demand Discount", Flight.DemandLevel.VERY_LOW, BigDecimal.valueOf(-0.15)));
        
        System.out.println("Created sample pricing rules");
    }
    
    /**
     * Helper method to create a flight with the given parameters.
     */
    private Flight createFlight(String flightNumber, String origin, String destination, 
                               LocalDateTime departureTime, Duration duration, String aircraftType, 
                               int capacity, Money basePrice) {
        FlightRoute route = FlightRoute.of(origin, destination);
        FlightSchedule schedule = FlightSchedule.withDuration(departureTime, duration);
        
        Flight flight = Flight.create(flightNumber, route, schedule, aircraftType, capacity, basePrice);
        return flightRepository.save(flight);
    }
}
