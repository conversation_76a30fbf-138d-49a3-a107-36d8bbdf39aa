package com.example.ddd.aviation.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for pricing calculation responses.
 * 
 * This DTO represents the result of a pricing calculation, including
 * the final price, applied discounts, and detailed breakdown of how
 * the price was calculated. It provides transparency for customers
 * and business users about pricing decisions.
 * 
 * Key Design Decisions:
 * - Immutable record for thread safety
 * - Includes detailed pricing breakdown for transparency
 * - Uses nested DTOs for complex structures
 * - Provides summary methods for different use cases
 * - Includes metadata about the calculation process
 * 
 * <AUTHOR> Aviation System
 */
public record PricingResponseDTO(
    String flightId,
    String flightNumber,
    String customerName,
    String loyaltyLevel,
    
    BigDecimal basePriceAmount,
    String basePriceCurrency,
    
    BigDecimal finalPriceAmount,
    String finalPriceCurrency,
    
    BigDecimal totalSavingsAmount,
    String totalSavingsCurrency,
    
    BigDecimal savingsPercentage,
    
    List<AppliedRuleDTO> appliedRules,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime bookingTime,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime calculationTime,
    
    Integer numberOfPassengers,
    BigDecimal totalCostAmount,
    String totalCostCurrency
) {
    
    /**
     * Creates a PricingResponseDTO from a domain PricingCalculation.
     * 
     * @param calculation Domain pricing calculation
     * @param numberOfPassengers Number of passengers
     * @return PricingResponseDTO representation
     */
    public static PricingResponseDTO fromDomain(
            com.example.ddd.aviation.domain.model.PricingCalculation calculation, 
            int numberOfPassengers) {
        
        List<AppliedRuleDTO> appliedRuleDTOs = calculation.appliedRules().stream()
            .map(AppliedRuleDTO::fromDomain)
            .toList();
        
        BigDecimal totalCost = calculation.finalPrice().amount()
            .multiply(BigDecimal.valueOf(numberOfPassengers));
        
        return new PricingResponseDTO(
            calculation.flight().getFlightId(),
            calculation.flight().getFlightNumber(),
            calculation.customer().getFullName(),
            calculation.customer().loyaltyLevel().getDisplayName(),
            calculation.basePrice().amount(),
            calculation.basePrice().currency().getCurrencyCode(),
            calculation.finalPrice().amount(),
            calculation.finalPrice().currency().getCurrencyCode(),
            calculation.totalSavings().amount(),
            calculation.totalSavings().currency().getCurrencyCode(),
            calculation.savingsPercentage(),
            appliedRuleDTOs,
            calculation.bookingTime(),
            calculation.calculationTime(),
            numberOfPassengers,
            totalCost,
            calculation.finalPrice().currency().getCurrencyCode()
        );
    }
    
    /**
     * Gets the number of applied rules.
     * 
     * @return Number of applied rules
     */
    public int getAppliedRuleCount() {
        return appliedRules.size();
    }
    
    /**
     * Checks if any discounts were applied.
     * 
     * @return true if discounts were applied
     */
    public boolean hasDiscounts() {
        return totalSavingsAmount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * Checks if the price was increased due to dynamic pricing.
     * 
     * @return true if price was increased
     */
    public boolean hasPriceIncrease() {
        return finalPriceAmount.compareTo(basePriceAmount) > 0;
    }
    
    /**
     * Gets a summary of the pricing calculation.
     * 
     * @return Pricing summary
     */
    public String getPricingSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("Flight %s: %s %s → %s %s", 
            flightNumber, basePriceCurrency, basePriceAmount, 
            finalPriceCurrency, finalPriceAmount));
        
        if (hasDiscounts()) {
            summary.append(String.format(" (%.1f%% savings)", 
                savingsPercentage.multiply(BigDecimal.valueOf(100))));
        }
        
        if (numberOfPassengers > 1) {
            summary.append(String.format(" × %d passengers = %s %s total", 
                numberOfPassengers, totalCostCurrency, totalCostAmount));
        }
        
        return summary.toString();
    }
    
    /**
     * Gets the savings per passenger.
     * 
     * @return Savings per passenger
     */
    public BigDecimal getSavingsPerPassenger() {
        return totalSavingsAmount;
    }
    
    /**
     * Gets the total savings for all passengers.
     * 
     * @return Total savings for all passengers
     */
    public BigDecimal getTotalSavingsForAllPassengers() {
        return totalSavingsAmount.multiply(BigDecimal.valueOf(numberOfPassengers));
    }
    
    /**
     * Nested DTO for applied pricing rules.
     */
    public record AppliedRuleDTO(
        String ruleId,
        String ruleName,
        String ruleType,
        String description,
        BigDecimal adjustmentAmount,
        String adjustmentCurrency,
        BigDecimal priceBeforeAmount,
        BigDecimal priceAfterAmount,
        String currency,
        Boolean isDiscount
    ) {
        
        /**
         * Creates an AppliedRuleDTO from a domain RuleApplication.
         * 
         * @param ruleApplication Domain rule application
         * @return AppliedRuleDTO representation
         */
        public static AppliedRuleDTO fromDomain(
                com.example.ddd.aviation.domain.model.PricingCalculation.RuleApplication ruleApplication) {
            
            return new AppliedRuleDTO(
                ruleApplication.rule().getRuleId(),
                ruleApplication.rule().getRuleName(),
                ruleApplication.rule().getRuleType().getDisplayName(),
                ruleApplication.rule().getDescription(),
                ruleApplication.adjustment().amount(),
                ruleApplication.adjustment().currency().getCurrencyCode(),
                ruleApplication.priceBefore().amount(),
                ruleApplication.priceAfter().amount(),
                ruleApplication.priceBefore().currency().getCurrencyCode(),
                ruleApplication.isDiscount()
            );
        }
        
        /**
         * Gets the savings from this rule.
         * 
         * @return Savings amount (positive for discounts)
         */
        public BigDecimal getSavings() {
            if (isDiscount) {
                return priceBeforeAmount.subtract(priceAfterAmount);
            }
            return BigDecimal.ZERO;
        }
        
        /**
         * Gets a description of the rule effect.
         * 
         * @return Rule effect description
         */
        public String getEffectDescription() {
            if (isDiscount) {
                return String.format("Saved %s %s", currency, getSavings());
            } else {
                BigDecimal increase = priceAfterAmount.subtract(priceBeforeAmount);
                return String.format("Increased by %s %s", currency, increase);
            }
        }
    }
}
