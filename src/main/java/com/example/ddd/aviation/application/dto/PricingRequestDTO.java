package com.example.ddd.aviation.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * Data Transfer Object for pricing calculation requests.
 * 
 * This DTO represents a request to calculate ticket pricing for a specific
 * flight and customer combination. It includes all necessary information
 * for the pricing engine to evaluate applicable rules and calculate final prices.
 * 
 * Key Design Decisions:
 * - Immutable record for thread safety
 * - Includes validation annotations for input validation
 * - Separates customer info from flight info for flexibility
 * - Uses primitive types for JSON serialization
 * - Provides clear field documentation for API consumers
 * 
 * <AUTHOR> Aviation System
 */
public record PricingRequestDTO(
    @NotBlank(message = "Flight ID is required")
    String flightId,
    
    @NotBlank(message = "Customer ID is required")
    String customerId,
    
    @NotBlank(message = "First name is required")
    String firstName,
    
    @NotBlank(message = "Last name is required")
    String lastName,
    
    @NotBlank(message = "Email is required")
    @Email(message = "Email must be valid")
    String email,
    
    @NotBlank(message = "Loyalty level is required")
    String loyaltyLevel,
    
    @Min(value = 0, message = "Total flights cannot be negative")
    Integer totalFlights,
    
    Boolean isFrequentFlyer,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime bookingTime,
    
    @Min(value = 1, message = "Number of passengers must be at least 1")
    Integer numberOfPassengers
) {
    
    /**
     * Constructor with default values.
     */
    public PricingRequestDTO {
        // Set default values for optional fields
        if (totalFlights == null) {
            totalFlights = 0;
        }
        if (isFrequentFlyer == null) {
            isFrequentFlyer = totalFlights >= 12;
        }
        if (bookingTime == null) {
            bookingTime = LocalDateTime.now();
        }
        if (numberOfPassengers == null) {
            numberOfPassengers = 1;
        }
    }
    
    /**
     * Creates a domain CustomerInfo object from this DTO.
     * 
     * @return CustomerInfo domain object
     */
    public com.example.ddd.aviation.domain.model.CustomerInfo toCustomerInfo() {
        com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel level = 
            com.example.ddd.aviation.domain.model.CustomerInfo.LoyaltyLevel.valueOf(loyaltyLevel.toUpperCase());
        
        return com.example.ddd.aviation.domain.model.CustomerInfo.existingCustomer(
            customerId, firstName, lastName, email, level, totalFlights);
    }
    
    /**
     * Gets the customer's full name.
     * 
     * @return Full name
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    /**
     * Checks if this is a multi-passenger booking.
     * 
     * @return true if more than one passenger
     */
    public boolean isMultiPassenger() {
        return numberOfPassengers > 1;
    }
    
    /**
     * Gets a summary of the pricing request.
     * 
     * @return Request summary
     */
    public String getSummary() {
        return String.format("Pricing request for %s (%s) - Flight %s, %d passenger(s)", 
            getFullName(), loyaltyLevel, flightId, numberOfPassengers);
    }
}
