package com.example.ddd.aviation.application.dto;

/**
 * Data Transfer Object for system-wide pricing statistics.
 * 
 * This DTO provides overall statistics about the pricing system,
 * including flight counts and rule distribution.
 * 
 * <AUTHOR> Aviation System
 */
public record SystemPricingStatsDTO(
    Long totalFlights,
    Long bookableFlights,
    Long totalRules,
    Long activeRules,
    Long seasonalDiscountRules,
    Long loyaltyDiscountRules,
    Long earlyBirdDiscountRules,
    Long lastMinuteDealRules,
    Long dynamicPricingRules
) {
    
    /**
     * Gets the percentage of flights that are bookable.
     * 
     * @return Bookable flight percentage
     */
    public Double getBookableFlightPercentage() {
        if (totalFlights == 0) {
            return 0.0;
        }
        return (bookableFlights.doubleValue() / totalFlights.doubleValue()) * 100.0;
    }
    
    /**
     * Gets the percentage of rules that are active.
     * 
     * @return Active rule percentage
     */
    public Double getActiveRulePercentage() {
        if (totalRules == 0) {
            return 0.0;
        }
        return (activeRules.doubleValue() / totalRules.doubleValue()) * 100.0;
    }
    
    /**
     * Gets a summary of rule distribution.
     * 
     * @return Rule distribution summary
     */
    public String getRuleDistributionSummary() {
        return String.format(
            "Seasonal: %d, Loyalty: %d, Early Bird: %d, Last Minute: %d, Dynamic: %d",
            seasonalDiscountRules, loyaltyDiscountRules, earlyBirdDiscountRules,
            lastMinuteDealRules, dynamicPricingRules
        );
    }
}
