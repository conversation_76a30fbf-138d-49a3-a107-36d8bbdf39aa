package com.example.ddd.aviation.application.service;

import com.example.ddd.aviation.application.dto.*;
import com.example.ddd.aviation.domain.model.*;
import com.example.ddd.aviation.domain.repository.FlightRepository;
import com.example.ddd.aviation.domain.repository.PricingRuleRepository;
import com.example.ddd.aviation.domain.service.PricingService;
import com.example.ddd.aviation.domain.service.RuleEvaluationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Application Service for ticket pricing operations.
 * 
 * This service orchestrates domain operations for ticket pricing,
 * coordinating between domain services and repositories to fulfill
 * use cases. It handles DTO conversions and transaction management.
 * 
 * Key Design Decisions:
 * - Orchestrates domain services without containing business logic
 * - Handles DTO conversions between API and domain layers
 * - Manages transactions for consistency
 * - Provides use case-focused methods
 * - Validates input and handles errors appropriately
 * 
 * <AUTHOR> Aviation System
 */
@Service
@Transactional(readOnly = true)
public class TicketPricingApplicationService {
    
    private final FlightRepository flightRepository;
    private final PricingRuleRepository pricingRuleRepository;
    private final PricingService pricingService;
    private final RuleEvaluationService ruleEvaluationService;
    
    public TicketPricingApplicationService(
            FlightRepository flightRepository,
            PricingRuleRepository pricingRuleRepository,
            PricingService pricingService,
            RuleEvaluationService ruleEvaluationService) {
        this.flightRepository = flightRepository;
        this.pricingRuleRepository = pricingRuleRepository;
        this.pricingService = pricingService;
        this.ruleEvaluationService = ruleEvaluationService;
    }
    
    /**
     * Calculates ticket price for a specific flight and customer.
     * 
     * @param request Pricing calculation request
     * @return Detailed pricing response
     * @throws IllegalArgumentException if flight not found or not bookable
     */
    public PricingResponseDTO calculateTicketPrice(PricingRequestDTO request) {
        // Find the flight
        Flight flight = flightRepository.findById(request.flightId())
            .orElseThrow(() -> new IllegalArgumentException("Flight not found: " + request.flightId()));
        
        // Validate flight is bookable
        if (!flight.isBookable()) {
            throw new IllegalArgumentException("Flight is not available for booking: " + flight.getStatus());
        }
        
        // Convert customer information
        CustomerInfo customer = request.toCustomerInfo();
        
        // Calculate pricing
        PricingCalculation calculation = pricingService.calculatePrice(
            flight, customer, request.bookingTime());
        
        // Convert to DTO and return
        return PricingResponseDTO.fromDomain(calculation, request.numberOfPassengers());
    }
    
    /**
     * Gets a quick price estimate for a flight and customer.
     * 
     * @param flightId Flight identifier
     * @param loyaltyLevel Customer loyalty level
     * @return Quick price estimate
     */
    public Money getQuickPriceEstimate(String flightId, String loyaltyLevel) {
        Flight flight = flightRepository.findById(flightId)
            .orElseThrow(() -> new IllegalArgumentException("Flight not found: " + flightId));
        
        // Create minimal customer info for estimate
        CustomerInfo.LoyaltyLevel level = CustomerInfo.LoyaltyLevel.valueOf(loyaltyLevel.toUpperCase());
        CustomerInfo customer = CustomerInfo.newCustomer("temp", "Customer", "Estimate", "<EMAIL>");
        
        return pricingService.calculateQuickEstimate(flight, customer);
    }
    
    /**
     * Finds available flights for a route and date.
     * 
     * @param origin Origin airport code
     * @param destination Destination airport code
     * @param departureDate Departure date
     * @return List of available flights
     */
    public List<FlightDTO> findAvailableFlights(String origin, String destination, LocalDate departureDate) {
        FlightRoute route = FlightRoute.of(origin, destination);
        List<Flight> flights = flightRepository.findBookableFlightsByRouteAndDate(route, departureDate);
        
        return flights.stream()
            .map(FlightDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets all available flights.
     * 
     * @return List of all bookable flights
     */
    public List<FlightDTO> getAllAvailableFlights() {
        List<Flight> flights = flightRepository.findBookableFlights();
        
        return flights.stream()
            .map(FlightDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets flight details by ID.
     * 
     * @param flightId Flight identifier
     * @return Flight details
     */
    public Optional<FlightDTO> getFlightById(String flightId) {
        return flightRepository.findById(flightId)
            .map(FlightDTO::fromDomain);
    }
    
    /**
     * Gets all active pricing rules.
     * 
     * @return List of active pricing rules
     */
    public List<PricingRuleDTO> getActivePricingRules() {
        List<PricingRule> rules = pricingRuleRepository.findActiveRules();
        
        return rules.stream()
            .map(PricingRuleDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets pricing rules by type.
     * 
     * @param ruleType Rule type
     * @return List of rules of the specified type
     */
    public List<PricingRuleDTO> getPricingRulesByType(String ruleType) {
        PricingRule.RuleType type = PricingRule.RuleType.valueOf(ruleType.toUpperCase());
        List<PricingRule> rules = pricingRuleRepository.findActiveRulesByType(type);
        
        return rules.stream()
            .map(PricingRuleDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * Gets applicable pricing rules for a specific flight and customer.
     * 
     * @param flightId Flight identifier
     * @param request Customer information
     * @return List of applicable rules
     */
    public List<PricingRuleDTO> getApplicablePricingRules(String flightId, PricingRequestDTO request) {
        Flight flight = flightRepository.findById(flightId)
            .orElseThrow(() -> new IllegalArgumentException("Flight not found: " + flightId));
        
        CustomerInfo customer = request.toCustomerInfo();
        
        List<PricingRule> applicableRules = ruleEvaluationService.getApplicableRules(
            flight, customer, request.bookingTime());
        
        return applicableRules.stream()
            .map(PricingRuleDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * Creates a new pricing rule.
     * 
     * @param request Rule creation request
     * @return Created rule
     */
    @Transactional
    public PricingRuleDTO createPricingRule(CreatePricingRuleDTO request) {
        // This would typically include more validation and business logic
        // For now, we'll create a simple seasonal discount as an example
        PricingRule rule = PricingRule.createSeasonalDiscount(
            request.ruleName(),
            FlightSchedule.Season.valueOf(request.targetSeason().toUpperCase()),
            request.discountPercentage()
        );
        
        PricingRule savedRule = pricingRuleRepository.save(rule);
        return PricingRuleDTO.fromDomain(savedRule);
    }
    
    /**
     * Gets pricing statistics for a flight.
     * 
     * @param flightId Flight identifier
     * @return Pricing statistics
     */
    public FlightPricingStatsDTO getFlightPricingStats(String flightId) {
        Flight flight = flightRepository.findById(flightId)
            .orElseThrow(() -> new IllegalArgumentException("Flight not found: " + flightId));
        
        // Calculate stats for different customer types
        CustomerInfo bronzeCustomer = CustomerInfo.newCustomer("bronze", "Bronze", "Customer", "<EMAIL>");
        CustomerInfo goldCustomer = CustomerInfo.existingCustomer("gold", "Gold", "Customer", "<EMAIL>", 
            CustomerInfo.LoyaltyLevel.GOLD, 30);
        
        LocalDateTime now = LocalDateTime.now();
        
        Money bronzePrice = pricingService.calculateQuickEstimate(flight, bronzeCustomer);
        Money goldPrice = pricingService.calculateQuickEstimate(flight, goldCustomer);
        
        return new FlightPricingStatsDTO(
            flightId,
            flight.getFlightNumber(),
            flight.getBasePrice().amount(),
            flight.getBasePrice().currency().getCurrencyCode(),
            bronzePrice.amount(),
            goldPrice.amount(),
            flight.getDemandLevel().getDisplayName(),
            flight.getOccupancyRate(),
            ruleEvaluationService.hasApplicableSeasonalDiscounts(flight),
            ruleEvaluationService.hasApplicableEarlyBirdDiscounts(flight, now),
            ruleEvaluationService.hasApplicableLastMinuteDeals(flight, now)
        );
    }
    
    /**
     * Gets system-wide pricing statistics.
     * 
     * @return System pricing statistics
     */
    public SystemPricingStatsDTO getSystemPricingStats() {
        long totalFlights = flightRepository.count();
        long bookableFlights = flightRepository.findBookableFlights().size();
        long totalRules = pricingRuleRepository.count();
        long activeRules = pricingRuleRepository.countActiveRules();
        
        return new SystemPricingStatsDTO(
            totalFlights,
            bookableFlights,
            totalRules,
            activeRules,
            pricingRuleRepository.countActiveRulesByType(PricingRule.RuleType.SEASONAL_DISCOUNT),
            pricingRuleRepository.countActiveRulesByType(PricingRule.RuleType.LOYALTY_DISCOUNT),
            pricingRuleRepository.countActiveRulesByType(PricingRule.RuleType.EARLY_BIRD_DISCOUNT),
            pricingRuleRepository.countActiveRulesByType(PricingRule.RuleType.LAST_MINUTE_DEAL),
            pricingRuleRepository.countActiveRulesByType(PricingRule.RuleType.DYNAMIC_PRICING)
        );
    }
}
