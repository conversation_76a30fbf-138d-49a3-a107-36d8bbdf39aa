package com.example.ddd.aviation.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Data Transfer Object for Flight information.
 * 
 * This DTO represents flight data for API communication between the
 * application layer and external clients. It provides a stable contract
 * for flight information exchange while hiding internal domain complexity.
 * 
 * Key Design Decisions:
 * - Immutable record for thread safety and clarity
 * - Uses primitive types and simple objects for JSON serialization
 * - Includes computed fields for client convenience
 * - Provides clear field documentation for API consumers
 * - Uses Jackson annotations for proper JSON formatting
 * 
 * <AUTHOR> Aviation System
 */
public record FlightDTO(
    String flightId,
    String flightNumber,
    String origin,
    String destination,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime departureTime,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime arrivalTime,
    
    String aircraftType,
    Integer totalCapacity,
    Integer bookedSeats,
    Integer availableSeats,
    Double occupancyRate,
    
    BigDecimal basePriceAmount,
    String basePriceCurrency,
    
    String status,
    String demandLevel,
    String season,
    String timeOfDay,
    Boolean isDomestic,
    Boolean isBookable,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime createdAt,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime updatedAt
) {
    
    /**
     * Creates a FlightDTO from a domain Flight object.
     * 
     * @param flight Domain flight object
     * @return FlightDTO representation
     */
    public static FlightDTO fromDomain(com.example.ddd.aviation.domain.model.Flight flight) {
        return new FlightDTO(
            flight.getFlightId(),
            flight.getFlightNumber(),
            flight.getRoute().origin(),
            flight.getRoute().destination(),
            flight.getSchedule().departureTime(),
            flight.getSchedule().arrivalTime(),
            flight.getAircraftType(),
            flight.getTotalCapacity(),
            flight.getBookedSeats(),
            flight.getAvailableSeats(),
            flight.getOccupancyRate(),
            flight.getBasePrice().amount(),
            flight.getBasePrice().currency().getCurrencyCode(),
            flight.getStatus().getDisplayName(),
            flight.getDemandLevel().getDisplayName(),
            flight.getSchedule().getSeason().getDisplayName(),
            flight.getSchedule().getTimeOfDay().getDisplayName(),
            flight.isDomestic(),
            flight.isBookable(),
            flight.getCreatedAt(),
            flight.getUpdatedAt()
        );
    }
    
    /**
     * Gets a summary description of the flight.
     * 
     * @return Flight summary string
     */
    public String getSummary() {
        return String.format("%s: %s → %s at %s (%s)", 
            flightNumber, origin, destination, 
            departureTime.toLocalTime(), status);
    }
    
    /**
     * Gets the route description.
     * 
     * @return Route description
     */
    public String getRouteDescription() {
        return String.format("%s → %s", origin, destination);
    }
    
    /**
     * Gets the capacity description.
     * 
     * @return Capacity description
     */
    public String getCapacityDescription() {
        return String.format("%d/%d seats (%.1f%% full)", 
            bookedSeats, totalCapacity, occupancyRate * 100);
    }
    
    /**
     * Checks if the flight is nearly full (>90% occupancy).
     * 
     * @return true if nearly full
     */
    public boolean isNearlyFull() {
        return occupancyRate > 0.9;
    }
    
    /**
     * Checks if the flight has low occupancy (<30%).
     * 
     * @return true if low occupancy
     */
    public boolean hasLowOccupancy() {
        return occupancyRate < 0.3;
    }
}
