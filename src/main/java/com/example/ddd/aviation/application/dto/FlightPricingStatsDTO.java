package com.example.ddd.aviation.application.dto;

import java.math.BigDecimal;

/**
 * Data Transfer Object for flight pricing statistics.
 * 
 * This DTO provides statistical information about pricing for a specific flight,
 * including price variations for different customer types and applicable discounts.
 * 
 * <AUTHOR> Aviation System
 */
public record FlightPricingStatsDTO(
    String flightId,
    String flightNumber,
    BigDecimal basePriceAmount,
    String basePriceCurrency,
    BigDecimal bronzeCustomerPrice,
    BigDecimal goldCustomerPrice,
    String demandLevel,
    Double occupancyRate,
    Boolean hasSeasonalDiscounts,
    Boolean hasEarlyBirdDiscounts,
    Boolean hasLastMinuteDeals
) {
    
    /**
     * Gets the price difference between gold and bronze customers.
     * 
     * @return Price difference
     */
    public BigDecimal getLoyaltyPriceDifference() {
        return bronzeCustomerPrice.subtract(goldCustomerPrice);
    }
    
    /**
     * Gets the loyalty discount percentage for gold customers.
     * 
     * @return Loyalty discount percentage
     */
    public BigDecimal getLoyaltyDiscountPercentage() {
        if (bronzeCustomerPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return getLoyaltyPriceDifference()
            .divide(bronzeCustomerPrice, 4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * Gets a summary of available discounts.
     * 
     * @return Discount summary
     */
    public String getDiscountSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (Boolean.TRUE.equals(hasSeasonalDiscounts)) {
            summary.append("Seasonal discounts available; ");
        }
        
        if (Boolean.TRUE.equals(hasEarlyBirdDiscounts)) {
            summary.append("Early bird discounts available; ");
        }
        
        if (Boolean.TRUE.equals(hasLastMinuteDeals)) {
            summary.append("Last-minute deals available; ");
        }
        
        String result = summary.toString();
        return result.isEmpty() ? "No special discounts available" : result.substring(0, result.length() - 2);
    }
}
