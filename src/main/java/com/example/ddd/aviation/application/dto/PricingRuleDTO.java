package com.example.ddd.aviation.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Data Transfer Object for PricingRule information.
 * 
 * This DTO represents pricing rule data for API communication,
 * providing a stable contract for rule information exchange
 * while hiding internal domain complexity.
 * 
 * Key Design Decisions:
 * - Immutable record for thread safety
 * - Uses primitive types for JSON serialization
 * - Includes rule condition details for transparency
 * - Provides computed fields for client convenience
 * - Uses Jackson annotations for proper JSON formatting
 * 
 * <AUTHOR> Aviation System
 */
public record PricingRuleDTO(
    String ruleId,
    String ruleName,
    String ruleType,
    String description,
    BigDecimal discountPercentage,
    Boolean isActive,
    Integer priority,
    
    // Rule conditions
    String targetSeason,
    String minimumLoyaltyLevel,
    Integer minimumDaysInAdvance,
    Integer maximumHoursBeforeDeparture,
    String targetDemandLevel,
    String targetRouteDistance,
    String targetTimeOfDay,
    Boolean requiresFrequentFly<PERSON>,
    <PERSON>olean domesticOnly,
    Boolean internationalOnly,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime createdAt,
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    LocalDateTime updatedAt
) {
    
    /**
     * Creates a PricingRuleDTO from a domain PricingRule object.
     * 
     * @param rule Domain pricing rule object
     * @return PricingRuleDTO representation
     */
    public static PricingRuleDTO fromDomain(com.example.ddd.aviation.domain.model.PricingRule rule) {
        var conditions = rule.getConditions();
        
        return new PricingRuleDTO(
            rule.getRuleId(),
            rule.getRuleName(),
            rule.getRuleType().getDisplayName(),
            rule.getDescription(),
            rule.getDiscountPercentage(),
            rule.isActive(),
            rule.getPriority(),
            conditions.targetSeason().map(s -> s.getDisplayName()).orElse(null),
            conditions.minimumLoyaltyLevel().map(l -> l.getDisplayName()).orElse(null),
            conditions.minimumDaysInAdvance().orElse(null),
            conditions.maximumHoursBeforeDeparture().orElse(null),
            conditions.targetDemandLevel().map(d -> d.getDisplayName()).orElse(null),
            conditions.targetRouteDistance().map(r -> r.getDisplayName()).orElse(null),
            conditions.targetTimeOfDay().map(t -> t.getDisplayName()).orElse(null),
            conditions.requiresFrequentFlyer().orElse(null),
            conditions.domesticOnly().orElse(null),
            conditions.internationalOnly().orElse(null),
            rule.getCreatedAt(),
            rule.getUpdatedAt()
        );
    }
    
    /**
     * Gets the discount percentage as a formatted string.
     * 
     * @return Formatted discount percentage
     */
    public String getFormattedDiscountPercentage() {
        if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0) {
            return String.format("%.1f%% discount", discountPercentage.multiply(BigDecimal.valueOf(100)));
        } else {
            return String.format("%.1f%% increase", discountPercentage.multiply(BigDecimal.valueOf(-100)));
        }
    }
    
    /**
     * Gets a summary of the rule conditions.
     * 
     * @return Conditions summary
     */
    public String getConditionsSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (targetSeason != null) {
            summary.append("Season: ").append(targetSeason).append("; ");
        }
        
        if (minimumLoyaltyLevel != null) {
            summary.append("Loyalty: ").append(minimumLoyaltyLevel).append("+; ");
        }
        
        if (minimumDaysInAdvance != null) {
            summary.append("Advance: ").append(minimumDaysInAdvance).append("+ days; ");
        }
        
        if (maximumHoursBeforeDeparture != null) {
            summary.append("Last-minute: ").append(maximumHoursBeforeDeparture).append("h; ");
        }
        
        if (targetDemandLevel != null) {
            summary.append("Demand: ").append(targetDemandLevel).append("; ");
        }
        
        if (Boolean.TRUE.equals(requiresFrequentFlyer)) {
            summary.append("Frequent flyer required; ");
        }
        
        if (Boolean.TRUE.equals(domesticOnly)) {
            summary.append("Domestic only; ");
        }
        
        if (Boolean.TRUE.equals(internationalOnly)) {
            summary.append("International only; ");
        }
        
        String result = summary.toString();
        return result.isEmpty() ? "No specific conditions" : result.substring(0, result.length() - 2);
    }
    
    /**
     * Gets the rule status description.
     * 
     * @return Status description
     */
    public String getStatusDescription() {
        return isActive ? "Active" : "Inactive";
    }
    
    /**
     * Gets the priority description.
     * 
     * @return Priority description
     */
    public String getPriorityDescription() {
        return switch (priority) {
            case 1, 2 -> "Low Priority";
            case 3, 4, 5 -> "Medium Priority";
            case 6, 7, 8 -> "High Priority";
            case 9, 10 -> "Critical Priority";
            default -> "Priority " + priority;
        };
    }
    
    /**
     * Checks if this is a discount rule.
     * 
     * @return true if this rule provides discounts
     */
    public boolean isDiscountRule() {
        return discountPercentage.compareTo(BigDecimal.ZERO) > 0 && 
               !ruleType.equals("Dynamic Pricing");
    }
    
    /**
     * Checks if this is a dynamic pricing rule.
     * 
     * @return true if this is a dynamic pricing rule
     */
    public boolean isDynamicPricingRule() {
        return ruleType.equals("Dynamic Pricing");
    }
    
    /**
     * Gets a complete description of the rule.
     * 
     * @return Complete rule description
     */
    public String getCompleteDescription() {
        return String.format("%s (%s) - %s - %s - Priority: %d - %s", 
            ruleName, ruleType, getFormattedDiscountPercentage(), 
            getConditionsSummary(), priority, getStatusDescription());
    }
}
