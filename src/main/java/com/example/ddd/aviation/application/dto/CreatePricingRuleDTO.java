package com.example.ddd.aviation.application.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.math.BigDecimal;

/**
 * Data Transfer Object for creating new pricing rules.
 * 
 * This DTO represents a request to create a new pricing rule in the system.
 * It includes validation annotations to ensure data integrity and business
 * rule compliance.
 * 
 * Key Design Decisions:
 * - Immutable record for thread safety
 * - Comprehensive validation annotations
 * - Supports all rule types through flexible structure
 * - Uses primitive types for JSON serialization
 * - Provides clear field documentation
 * 
 * <AUTHOR> Aviation System
 */
public record CreatePricingRuleDTO(
    @NotBlank(message = "Rule name is required")
    String ruleName,
    
    @NotBlank(message = "Rule type is required")
    String ruleType,
    
    String description,
    
    @NotNull(message = "Discount percentage is required")
    @DecimalMin(value = "-1.0", message = "Discount percentage cannot be less than -100%")
    @DecimalMax(value = "1.0", message = "Discount percentage cannot be more than 100%")
    BigDecimal discountPercentage,
    
    @Min(value = 1, message = "Priority must be between 1 and 10")
    @Max(value = 10, message = "Priority must be between 1 and 10")
    Integer priority,
    
    // Rule condition fields
    String targetSeason,
    String minimumLoyaltyLevel,
    
    @Min(value = 0, message = "Minimum days in advance cannot be negative")
    Integer minimumDaysInAdvance,
    
    @Min(value = 0, message = "Maximum hours before departure cannot be negative")
    Integer maximumHoursBeforeDeparture,
    
    String targetDemandLevel,
    String targetRouteDistance,
    String targetTimeOfDay,
    Boolean requiresFrequentFlyer,
    Boolean domesticOnly,
    Boolean internationalOnly
) {
    
    /**
     * Constructor with default values.
     */
    public CreatePricingRuleDTO {
        // Set default priority if not provided
        if (priority == null) {
            priority = 5; // Medium priority
        }
        
        // Set default description if not provided
        if (description == null || description.trim().isEmpty()) {
            description = generateDefaultDescription(ruleName, ruleType, discountPercentage);
        }
    }
    
    /**
     * Generates a default description based on rule parameters.
     */
    private static String generateDefaultDescription(String ruleName, String ruleType, BigDecimal discountPercentage) {
        if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0) {
            return String.format("%s providing %.1f%% discount", 
                ruleName, discountPercentage.multiply(BigDecimal.valueOf(100)));
        } else {
            return String.format("%s with %.1f%% price adjustment", 
                ruleName, discountPercentage.multiply(BigDecimal.valueOf(100)));
        }
    }
    
    /**
     * Validates that the rule configuration is consistent.
     * 
     * @return true if configuration is valid
     */
    public boolean isValidConfiguration() {
        // Check for conflicting conditions
        if (Boolean.TRUE.equals(domesticOnly) && Boolean.TRUE.equals(internationalOnly)) {
            return false;
        }
        
        // Check for conflicting timing conditions
        if (minimumDaysInAdvance != null && maximumHoursBeforeDeparture != null) {
            // These are typically mutually exclusive (early bird vs last minute)
            return false;
        }
        
        // Validate rule type specific conditions
        return switch (ruleType.toUpperCase()) {
            case "SEASONAL_DISCOUNT" -> targetSeason != null;
            case "LOYALTY_DISCOUNT" -> minimumLoyaltyLevel != null;
            case "EARLY_BIRD_DISCOUNT" -> minimumDaysInAdvance != null;
            case "LAST_MINUTE_DEAL" -> maximumHoursBeforeDeparture != null;
            case "DYNAMIC_PRICING" -> targetDemandLevel != null;
            default -> true; // Allow other configurations
        };
    }
    
    /**
     * Gets a summary of the rule being created.
     * 
     * @return Rule summary
     */
    public String getRuleSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("%s (%s): ", ruleName, ruleType));
        
        if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0) {
            summary.append(String.format("%.1f%% discount", 
                discountPercentage.multiply(BigDecimal.valueOf(100))));
        } else {
            summary.append(String.format("%.1f%% increase", 
                discountPercentage.multiply(BigDecimal.valueOf(-100))));
        }
        
        summary.append(String.format(" (Priority: %d)", priority));
        
        return summary.toString();
    }
    
    /**
     * Gets the conditions summary for this rule.
     * 
     * @return Conditions summary
     */
    public String getConditionsSummary() {
        StringBuilder conditions = new StringBuilder();
        
        if (targetSeason != null) {
            conditions.append("Season: ").append(targetSeason).append("; ");
        }
        
        if (minimumLoyaltyLevel != null) {
            conditions.append("Loyalty: ").append(minimumLoyaltyLevel).append("+; ");
        }
        
        if (minimumDaysInAdvance != null) {
            conditions.append("Advance booking: ").append(minimumDaysInAdvance).append("+ days; ");
        }
        
        if (maximumHoursBeforeDeparture != null) {
            conditions.append("Last-minute: within ").append(maximumHoursBeforeDeparture).append(" hours; ");
        }
        
        if (targetDemandLevel != null) {
            conditions.append("Demand: ").append(targetDemandLevel).append("; ");
        }
        
        if (Boolean.TRUE.equals(requiresFrequentFlyer)) {
            conditions.append("Frequent flyer required; ");
        }
        
        if (Boolean.TRUE.equals(domesticOnly)) {
            conditions.append("Domestic flights only; ");
        }
        
        if (Boolean.TRUE.equals(internationalOnly)) {
            conditions.append("International flights only; ");
        }
        
        String result = conditions.toString();
        return result.isEmpty() ? "No specific conditions" : result.substring(0, result.length() - 2);
    }
}
