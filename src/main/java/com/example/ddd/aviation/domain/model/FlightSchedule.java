package com.example.ddd.aviation.domain.model;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * Value Object representing flight schedule information.
 * 
 * This immutable value object encapsulates departure and arrival times,
 * providing business logic for schedule-related operations and validations.
 * 
 * Key Design Decisions:
 * - Immutable to ensure schedule integrity
 * - Uses LocalDateTime for timezone-neutral scheduling
 * - Contains business logic for schedule validation and calculations
 * - Provides methods for pricing-related schedule analysis
 * 
 * <AUTHOR> Aviation System
 */
public record FlightSchedule(LocalDateTime departureTime, LocalDateTime arrivalTime) {
    
    /**
     * Creates a FlightSchedule with validation.
     * 
     * @param departureTime The departure date and time (cannot be null)
     * @param arrivalTime The arrival date and time (cannot be null)
     * @throws IllegalArgumentException if times are invalid or arrival is before departure
     */
    public FlightSchedule {
        Objects.requireNonNull(departureTime, "Departure time cannot be null");
        Objects.requireNonNull(arrivalTime, "Arrival time cannot be null");
        
        if (arrivalTime.isBefore(departureTime)) {
            throw new IllegalArgumentException("Arrival time cannot be before departure time");
        }
        
        if (departureTime.isBefore(LocalDateTime.now().minusHours(1))) {
            throw new IllegalArgumentException("Departure time cannot be in the past");
        }
        
        // Validate reasonable flight duration (max 24 hours for this demo)
        Duration flightDuration = Duration.between(departureTime, arrivalTime);
        if (flightDuration.toHours() > 24) {
            throw new IllegalArgumentException("Flight duration cannot exceed 24 hours");
        }
    }
    
    /**
     * Factory method to create a FlightSchedule.
     * 
     * @param departureTime The departure time
     * @param arrivalTime The arrival time
     * @return FlightSchedule instance
     */
    public static FlightSchedule of(LocalDateTime departureTime, LocalDateTime arrivalTime) {
        return new FlightSchedule(departureTime, arrivalTime);
    }
    
    /**
     * Factory method to create a FlightSchedule with duration.
     * 
     * @param departureTime The departure time
     * @param flightDuration The flight duration
     * @return FlightSchedule instance
     */
    public static FlightSchedule withDuration(LocalDateTime departureTime, Duration flightDuration) {
        Objects.requireNonNull(flightDuration, "Flight duration cannot be null");
        LocalDateTime arrivalTime = departureTime.plus(flightDuration);
        return new FlightSchedule(departureTime, arrivalTime);
    }
    
    /**
     * Gets the flight duration.
     * 
     * @return Duration of the flight
     */
    public Duration getFlightDuration() {
        return Duration.between(departureTime, arrivalTime);
    }
    
    /**
     * Gets the flight duration in hours.
     * 
     * @return Flight duration in hours
     */
    public long getFlightDurationHours() {
        return getFlightDuration().toHours();
    }
    
    /**
     * Checks if this is a red-eye flight (departing late night/early morning).
     * Red-eye flights typically depart between 9 PM and 5 AM.
     * 
     * @return true if this is a red-eye flight
     */
    public boolean isRedEyeFlight() {
        int hour = departureTime.getHour();
        return hour >= 21 || hour <= 5;
    }
    
    /**
     * Checks if this is a morning flight (departing between 6 AM and 11 AM).
     * 
     * @return true if this is a morning flight
     */
    public boolean isMorningFlight() {
        int hour = departureTime.getHour();
        return hour >= 6 && hour <= 11;
    }
    
    /**
     * Checks if this is an afternoon flight (departing between 12 PM and 5 PM).
     * 
     * @return true if this is an afternoon flight
     */
    public boolean isAfternoonFlight() {
        int hour = departureTime.getHour();
        return hour >= 12 && hour <= 17;
    }
    
    /**
     * Checks if this is an evening flight (departing between 6 PM and 8 PM).
     * 
     * @return true if this is an evening flight
     */
    public boolean isEveningFlight() {
        int hour = departureTime.getHour();
        return hour >= 18 && hour <= 20;
    }
    
    /**
     * Gets the time of day category for this flight.
     * 
     * @return TimeOfDay category
     */
    public TimeOfDay getTimeOfDay() {
        if (isMorningFlight()) return TimeOfDay.MORNING;
        if (isAfternoonFlight()) return TimeOfDay.AFTERNOON;
        if (isEveningFlight()) return TimeOfDay.EVENING;
        return TimeOfDay.RED_EYE;
    }
    
    /**
     * Calculates hours until departure from now.
     * 
     * @return Hours until departure (negative if in the past)
     */
    public long getHoursUntilDeparture() {
        return ChronoUnit.HOURS.between(LocalDateTime.now(), departureTime);
    }
    
    /**
     * Calculates days until departure from now.
     * 
     * @return Days until departure (negative if in the past)
     */
    public long getDaysUntilDeparture() {
        return ChronoUnit.DAYS.between(LocalDateTime.now().toLocalDate(), departureTime.toLocalDate());
    }
    
    /**
     * Checks if this flight is departing within the specified hours.
     * 
     * @param hours The number of hours to check
     * @return true if departing within the specified hours
     */
    public boolean isDepartingWithinHours(long hours) {
        long hoursUntil = getHoursUntilDeparture();
        return hoursUntil >= 0 && hoursUntil <= hours;
    }
    
    /**
     * Checks if this flight is departing within the specified days.
     * 
     * @param days The number of days to check
     * @return true if departing within the specified days
     */
    public boolean isDepartingWithinDays(long days) {
        long daysUntil = getDaysUntilDeparture();
        return daysUntil >= 0 && daysUntil <= days;
    }
    
    /**
     * Checks if this is a last-minute booking (within 24 hours).
     * 
     * @return true if departing within 24 hours
     */
    public boolean isLastMinute() {
        return isDepartingWithinHours(24);
    }
    
    /**
     * Checks if this qualifies for early bird pricing (more than 90 days in advance).
     * 
     * @return true if departing more than 90 days from now
     */
    public boolean isEarlyBird() {
        return getDaysUntilDeparture() > 90;
    }
    
    /**
     * Gets the season for the departure date.
     * This is used for seasonal pricing rules.
     * 
     * @return Season for the departure date
     */
    public Season getSeason() {
        int month = departureTime.getMonthValue();
        return switch (month) {
            case 12, 1, 2 -> Season.WINTER;
            case 3, 4, 5 -> Season.SPRING;
            case 6, 7, 8 -> Season.SUMMER;
            case 9, 10, 11 -> Season.FALL;
            default -> throw new IllegalStateException("Invalid month: " + month);
        };
    }
    
    /**
     * Enumeration for time of day categories.
     */
    public enum TimeOfDay {
        MORNING("Morning", "6 AM - 11 AM"),
        AFTERNOON("Afternoon", "12 PM - 5 PM"),
        EVENING("Evening", "6 PM - 8 PM"),
        RED_EYE("Red-eye", "9 PM - 5 AM");
        
        private final String displayName;
        private final String timeRange;
        
        TimeOfDay(String displayName, String timeRange) {
            this.displayName = displayName;
            this.timeRange = timeRange;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getTimeRange() {
            return timeRange;
        }
    }
    
    /**
     * Enumeration for seasons.
     */
    public enum Season {
        SPRING("Spring", "March - May"),
        SUMMER("Summer", "June - August"),
        FALL("Fall", "September - November"),
        WINTER("Winter", "December - February");
        
        private final String displayName;
        private final String months;
        
        Season(String displayName, String months) {
            this.displayName = displayName;
            this.months = months;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getMonths() {
            return months;
        }
    }
    
    @Override
    public String toString() {
        return String.format("Departure: %s, Arrival: %s (Duration: %d hours)", 
            departureTime, arrivalTime, getFlightDurationHours());
    }
}
