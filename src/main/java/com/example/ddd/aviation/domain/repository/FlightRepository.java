package com.example.ddd.aviation.domain.repository;

import com.example.ddd.aviation.domain.model.Flight;
import com.example.ddd.aviation.domain.model.FlightRoute;
import com.example.ddd.aviation.domain.model.FlightSchedule;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Flight aggregate root.
 * 
 * This interface defines the contract for persisting and retrieving Flight
 * entities. It follows DDD repository pattern principles by providing
 * domain-focused query methods rather than generic CRUD operations.
 * 
 * Key Design Decisions:
 * - Domain-focused query methods
 * - Returns domain objects, not data structures
 * - Uses domain value objects in method signatures
 * - Provides both single entity and collection queries
 * - Includes business-relevant search capabilities
 * 
 * <AUTHOR> Aviation System
 */
public interface FlightRepository {
    
    /**
     * Saves a flight entity.
     * 
     * @param flight The flight to save
     * @return The saved flight
     */
    Flight save(Flight flight);
    
    /**
     * Finds a flight by its unique identifier.
     * 
     * @param flightId The flight ID
     * @return Optional containing the flight if found
     */
    Optional<Flight> findById(String flightId);
    
    /**
     * Finds a flight by its flight number and departure date.
     * Flight numbers are typically unique per day.
     * 
     * @param flightNumber The flight number (e.g., "AA123")
     * @param departureDate The departure date
     * @return Optional containing the flight if found
     */
    Optional<Flight> findByFlightNumberAndDepartureDate(String flightNumber, LocalDate departureDate);
    
    /**
     * Finds all flights for a specific route on a given date.
     * 
     * @param route The flight route
     * @param departureDate The departure date
     * @return List of flights matching the criteria
     */
    List<Flight> findByRouteAndDepartureDate(FlightRoute route, LocalDate departureDate);
    
    /**
     * Finds flights departing from an origin to a destination within a date range.
     * 
     * @param origin Origin airport code
     * @param destination Destination airport code
     * @param startDate Start of date range (inclusive)
     * @param endDate End of date range (inclusive)
     * @return List of flights matching the criteria
     */
    List<Flight> findByOriginAndDestinationAndDepartureDateBetween(
        String origin, String destination, LocalDate startDate, LocalDate endDate);
    
    /**
     * Finds all bookable flights (scheduled status with available seats).
     * 
     * @return List of bookable flights
     */
    List<Flight> findBookableFlights();
    
    /**
     * Finds bookable flights for a specific route and date.
     * 
     * @param route The flight route
     * @param departureDate The departure date
     * @return List of bookable flights
     */
    List<Flight> findBookableFlightsByRouteAndDate(FlightRoute route, LocalDate departureDate);
    
    /**
     * Finds flights with high demand (>80% occupancy).
     * 
     * @return List of high-demand flights
     */
    List<Flight> findHighDemandFlights();
    
    /**
     * Finds flights with low demand (<30% occupancy).
     * 
     * @return List of low-demand flights
     */
    List<Flight> findLowDemandFlights();
    
    /**
     * Finds flights departing within the specified hours.
     * Useful for last-minute deal identification.
     * 
     * @param hours Number of hours from now
     * @return List of flights departing within the timeframe
     */
    List<Flight> findFlightsDepartingWithinHours(int hours);
    
    /**
     * Finds flights departing after the specified number of days.
     * Useful for early bird discount identification.
     * 
     * @param days Number of days from now
     * @return List of flights departing after the timeframe
     */
    List<Flight> findFlightsDepartingAfterDays(int days);
    
    /**
     * Finds flights by aircraft type.
     * 
     * @param aircraftType The aircraft type
     * @return List of flights using the specified aircraft
     */
    List<Flight> findByAircraftType(String aircraftType);
    
    /**
     * Finds domestic flights (within the same country).
     * 
     * @return List of domestic flights
     */
    List<Flight> findDomesticFlights();
    
    /**
     * Finds international flights (between different countries).
     * 
     * @return List of international flights
     */
    List<Flight> findInternationalFlights();
    
    /**
     * Finds flights by status.
     * 
     * @param status The flight status
     * @return List of flights with the specified status
     */
    List<Flight> findByStatus(Flight.FlightStatus status);
    
    /**
     * Finds flights with available seats greater than the specified minimum.
     * 
     * @param minimumSeats Minimum number of available seats
     * @return List of flights with sufficient availability
     */
    List<Flight> findFlightsWithAvailableSeats(int minimumSeats);
    
    /**
     * Finds flights departing between specific times.
     * 
     * @param startTime Start time (inclusive)
     * @param endTime End time (inclusive)
     * @return List of flights departing within the time range
     */
    List<Flight> findByDepartureTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Finds flights for a specific season.
     * 
     * @param season The season
     * @return List of flights departing in the specified season
     */
    List<Flight> findBySeason(FlightSchedule.Season season);
    
    /**
     * Finds flights by time of day category.
     * 
     * @param timeOfDay The time of day category
     * @return List of flights departing at the specified time of day
     */
    List<Flight> findByTimeOfDay(FlightSchedule.TimeOfDay timeOfDay);
    
    /**
     * Finds flights by demand level.
     * 
     * @param demandLevel The demand level
     * @return List of flights with the specified demand level
     */
    List<Flight> findByDemandLevel(Flight.DemandLevel demandLevel);
    
    /**
     * Counts the total number of flights.
     * 
     * @return Total flight count
     */
    long count();
    
    /**
     * Counts flights by status.
     * 
     * @param status The flight status
     * @return Number of flights with the specified status
     */
    long countByStatus(Flight.FlightStatus status);
    
    /**
     * Counts bookable flights for a route and date.
     * 
     * @param route The flight route
     * @param departureDate The departure date
     * @return Number of bookable flights
     */
    long countBookableFlightsByRouteAndDate(FlightRoute route, LocalDate departureDate);
    
    /**
     * Checks if a flight exists with the given flight number and departure date.
     * 
     * @param flightNumber The flight number
     * @param departureDate The departure date
     * @return true if flight exists
     */
    boolean existsByFlightNumberAndDepartureDate(String flightNumber, LocalDate departureDate);
    
    /**
     * Deletes a flight by its ID.
     * Note: This should be used carefully as it may affect referential integrity.
     * 
     * @param flightId The flight ID to delete
     */
    void deleteById(String flightId);
    
    /**
     * Deletes all flights.
     * Note: This is typically used only in testing scenarios.
     */
    void deleteAll();
}
