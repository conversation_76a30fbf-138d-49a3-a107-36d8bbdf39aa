package com.example.ddd.aviation.domain.model;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * Value Object representing conditions for pricing rule evaluation.
 * 
 * This immutable value object encapsulates the various conditions that
 * determine when a pricing rule should be applied. It provides methods
 * to evaluate these conditions against flight, customer, and booking context.
 * 
 * Key Design Decisions:
 * - Immutable value object for thread safety
 * - Uses Optional for nullable conditions
 * - Encapsulates condition evaluation logic
 * - Supports multiple condition types in a single object
 * - Factory methods for common condition patterns
 * 
 * <AUTHOR> Aviation System
 */
public record RuleConditions(
    Optional<FlightSchedule.Season> targetSeason,
    Optional<CustomerInfo.LoyaltyLevel> minimumLoyaltyLevel,
    Optional<Integer> minimumDaysInAdvance,
    Optional<Integer> maximumHoursBeforeDeparture,
    Optional<Flight.DemandLevel> targetDemandLevel,
    Optional<FlightRoute.RouteDistance> targetRouteDistance,
    Optional<FlightSchedule.TimeOfDay> targetTimeOfDay,
    Optional<Boolean> requiresFrequent<PERSON>ly<PERSON>,
    Optional<Boolean> domesticOnly,
    Optional<Boolean> internationalOnly
) {
    
    /**
     * Creates RuleConditions with validation.
     */
    public RuleConditions {
        // Validate that at least one condition is specified
        if (targetSeason.isEmpty() && minimumLoyaltyLevel.isEmpty() && 
            minimumDaysInAdvance.isEmpty() && maximumHoursBeforeDeparture.isEmpty() &&
            targetDemandLevel.isEmpty() && targetRouteDistance.isEmpty() &&
            targetTimeOfDay.isEmpty() && requiresFrequentFlyer.isEmpty() &&
            domesticOnly.isEmpty() && internationalOnly.isEmpty()) {
            throw new IllegalArgumentException("At least one condition must be specified");
        }
        
        // Validate logical consistency
        if (domesticOnly.isPresent() && internationalOnly.isPresent() &&
            domesticOnly.get() && internationalOnly.get()) {
            throw new IllegalArgumentException("Cannot require both domestic and international flights");
        }
        
        if (minimumDaysInAdvance.isPresent() && minimumDaysInAdvance.get() < 0) {
            throw new IllegalArgumentException("Minimum days in advance cannot be negative");
        }
        
        if (maximumHoursBeforeDeparture.isPresent() && maximumHoursBeforeDeparture.get() < 0) {
            throw new IllegalArgumentException("Maximum hours before departure cannot be negative");
        }
    }
    
    /**
     * Factory method for seasonal conditions.
     * 
     * @param season Target season
     * @return RuleConditions for season
     */
    public static RuleConditions forSeason(FlightSchedule.Season season) {
        Objects.requireNonNull(season, "Season cannot be null");
        return new RuleConditions(
            Optional.of(season), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for loyalty level conditions.
     * 
     * @param loyaltyLevel Minimum loyalty level
     * @return RuleConditions for loyalty level
     */
    public static RuleConditions forLoyaltyLevel(CustomerInfo.LoyaltyLevel loyaltyLevel) {
        Objects.requireNonNull(loyaltyLevel, "Loyalty level cannot be null");
        return new RuleConditions(
            Optional.empty(), Optional.of(loyaltyLevel), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for early bird conditions.
     * 
     * @param daysInAdvance Minimum days in advance
     * @return RuleConditions for early bird
     */
    public static RuleConditions forEarlyBird(int daysInAdvance) {
        if (daysInAdvance < 0) {
            throw new IllegalArgumentException("Days in advance cannot be negative");
        }
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.of(daysInAdvance), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for last-minute conditions.
     * 
     * @param hoursBeforeDeparture Maximum hours before departure
     * @return RuleConditions for last-minute
     */
    public static RuleConditions forLastMinute(int hoursBeforeDeparture) {
        if (hoursBeforeDeparture < 0) {
            throw new IllegalArgumentException("Hours before departure cannot be negative");
        }
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.of(hoursBeforeDeparture),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for demand level conditions.
     * 
     * @param demandLevel Target demand level
     * @return RuleConditions for demand level
     */
    public static RuleConditions forDemandLevel(Flight.DemandLevel demandLevel) {
        Objects.requireNonNull(demandLevel, "Demand level cannot be null");
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.of(demandLevel), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for frequent flyer conditions.
     * 
     * @return RuleConditions requiring frequent flyer status
     */
    public static RuleConditions forFrequentFlyers() {
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.of(true),
            Optional.empty(), Optional.empty()
        );
    }
    
    /**
     * Factory method for domestic flight conditions.
     * 
     * @return RuleConditions for domestic flights only
     */
    public static RuleConditions forDomesticFlights() {
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.of(true), Optional.empty()
        );
    }
    
    /**
     * Factory method for international flight conditions.
     * 
     * @return RuleConditions for international flights only
     */
    public static RuleConditions forInternationalFlights() {
        return new RuleConditions(
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
            Optional.empty(), Optional.of(true)
        );
    }
    
    /**
     * Evaluates if these conditions match the given context.
     * 
     * @param flight The flight
     * @param customer The customer
     * @param bookingTime The booking time
     * @return true if all conditions are met
     */
    public boolean matches(Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(customer, "Customer cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        
        // Check season condition
        if (targetSeason.isPresent()) {
            FlightSchedule.Season flightSeason = flight.getSchedule().getSeason();
            if (!targetSeason.get().equals(flightSeason)) {
                return false;
            }
        }
        
        // Check loyalty level condition
        if (minimumLoyaltyLevel.isPresent()) {
            CustomerInfo.LoyaltyLevel customerLevel = customer.loyaltyLevel();
            if (customerLevel.ordinal() < minimumLoyaltyLevel.get().ordinal()) {
                return false;
            }
        }
        
        // Check early bird condition
        if (minimumDaysInAdvance.isPresent()) {
            long daysUntilDeparture = flight.getSchedule().getDaysUntilDeparture();
            if (daysUntilDeparture < minimumDaysInAdvance.get()) {
                return false;
            }
        }
        
        // Check last-minute condition
        if (maximumHoursBeforeDeparture.isPresent()) {
            long hoursUntilDeparture = flight.getSchedule().getHoursUntilDeparture();
            if (hoursUntilDeparture > maximumHoursBeforeDeparture.get()) {
                return false;
            }
        }
        
        // Check demand level condition
        if (targetDemandLevel.isPresent()) {
            Flight.DemandLevel flightDemand = flight.getDemandLevel();
            if (!targetDemandLevel.get().equals(flightDemand)) {
                return false;
            }
        }
        
        // Check route distance condition
        if (targetRouteDistance.isPresent()) {
            FlightRoute.RouteDistance routeDistance = flight.getRoute().getDistanceCategory();
            if (!targetRouteDistance.get().equals(routeDistance)) {
                return false;
            }
        }
        
        // Check time of day condition
        if (targetTimeOfDay.isPresent()) {
            FlightSchedule.TimeOfDay flightTimeOfDay = flight.getSchedule().getTimeOfDay();
            if (!targetTimeOfDay.get().equals(flightTimeOfDay)) {
                return false;
            }
        }
        
        // Check frequent flyer condition
        if (requiresFrequentFlyer.isPresent() && requiresFrequentFlyer.get()) {
            if (!customer.isFrequentFlyer()) {
                return false;
            }
        }
        
        // Check domestic/international conditions
        if (domesticOnly.isPresent() && domesticOnly.get()) {
            if (!flight.isDomestic()) {
                return false;
            }
        }
        
        if (internationalOnly.isPresent() && internationalOnly.get()) {
            if (!flight.isInternational()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Checks if these conditions have any overlap with other conditions.
     * This is useful for detecting conflicting rules.
     * 
     * @param other Other conditions to check against
     * @return true if there's potential overlap
     */
    public boolean hasOverlapWith(RuleConditions other) {
        Objects.requireNonNull(other, "Other conditions cannot be null");
        
        // If both have season conditions, they must match
        if (this.targetSeason.isPresent() && other.targetSeason.isPresent()) {
            return this.targetSeason.equals(other.targetSeason);
        }
        
        // If both have loyalty conditions, check for overlap
        if (this.minimumLoyaltyLevel.isPresent() && other.minimumLoyaltyLevel.isPresent()) {
            // There's overlap if the ranges intersect
            return true;
        }
        
        // If both have timing conditions, check for overlap
        if (this.minimumDaysInAdvance.isPresent() && other.maximumHoursBeforeDeparture.isPresent()) {
            // These are mutually exclusive (early bird vs last minute)
            return false;
        }
        
        if (this.maximumHoursBeforeDeparture.isPresent() && other.minimumDaysInAdvance.isPresent()) {
            // These are mutually exclusive (last minute vs early bird)
            return false;
        }
        
        // If both have domestic/international restrictions that conflict
        if (this.domesticOnly.isPresent() && other.internationalOnly.isPresent()) {
            return !(this.domesticOnly.get() && other.internationalOnly.get());
        }
        
        if (this.internationalOnly.isPresent() && other.domesticOnly.isPresent()) {
            return !(this.internationalOnly.get() && other.domesticOnly.get());
        }
        
        // Default to assuming overlap for safety
        return true;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Conditions{");
        boolean first = true;
        
        if (targetSeason.isPresent()) {
            sb.append("season=").append(targetSeason.get().getDisplayName());
            first = false;
        }
        
        if (minimumLoyaltyLevel.isPresent()) {
            if (!first) sb.append(", ");
            sb.append("loyalty>=").append(minimumLoyaltyLevel.get().getDisplayName());
            first = false;
        }
        
        if (minimumDaysInAdvance.isPresent()) {
            if (!first) sb.append(", ");
            sb.append("advance>=").append(minimumDaysInAdvance.get()).append("days");
            first = false;
        }
        
        if (maximumHoursBeforeDeparture.isPresent()) {
            if (!first) sb.append(", ");
            sb.append("lastMinute<=").append(maximumHoursBeforeDeparture.get()).append("hours");
            first = false;
        }
        
        if (targetDemandLevel.isPresent()) {
            if (!first) sb.append(", ");
            sb.append("demand=").append(targetDemandLevel.get().getDisplayName());
            first = false;
        }
        
        if (requiresFrequentFlyer.isPresent() && requiresFrequentFlyer.get()) {
            if (!first) sb.append(", ");
            sb.append("frequentFlyer=true");
            first = false;
        }
        
        if (domesticOnly.isPresent() && domesticOnly.get()) {
            if (!first) sb.append(", ");
            sb.append("domesticOnly=true");
            first = false;
        }
        
        if (internationalOnly.isPresent() && internationalOnly.get()) {
            if (!first) sb.append(", ");
            sb.append("internationalOnly=true");
        }
        
        sb.append("}");
        return sb.toString();
    }
}
