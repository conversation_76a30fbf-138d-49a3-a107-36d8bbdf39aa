package com.example.ddd.aviation.domain.model;

import java.util.Objects;

/**
 * Value Object representing customer information for pricing calculations.
 * 
 * This immutable value object encapsulates customer details relevant to
 * pricing decisions, including loyalty status and booking history.
 * 
 * Key Design Decisions:
 * - Immutable to ensure data integrity
 * - Contains only pricing-relevant customer information
 * - Encapsulates loyalty level business logic
 * - Provides methods for pricing rule evaluation
 * 
 * <AUTHOR> Aviation System
 */
public record CustomerInfo(
    String customerId,
    String firstName,
    String lastName,
    String email,
    LoyaltyLevel loyaltyLevel,
    int totalFlights,
    boolean isFrequentFlyer
) {
    
    /**
     * Creates CustomerInfo with validation.
     * 
     * @param customerId Unique customer identifier (cannot be null or empty)
     * @param firstName Customer's first name (cannot be null or empty)
     * @param lastName Customer's last name (cannot be null or empty)
     * @param email Customer's email (cannot be null or empty)
     * @param loyaltyLevel Customer's loyalty level (cannot be null)
     * @param totalFlights Total number of flights taken (cannot be negative)
     * @param isFrequentFlyer Whether customer is a frequent flyer
     */
    public CustomerInfo {
        Objects.requireNonNull(customerId, "Customer ID cannot be null");
        Objects.requireNonNull(firstName, "First name cannot be null");
        Objects.requireNonNull(lastName, "Last name cannot be null");
        Objects.requireNonNull(email, "Email cannot be null");
        Objects.requireNonNull(loyaltyLevel, "Loyalty level cannot be null");
        
        if (customerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Customer ID cannot be empty");
        }
        
        if (firstName.trim().isEmpty()) {
            throw new IllegalArgumentException("First name cannot be empty");
        }
        
        if (lastName.trim().isEmpty()) {
            throw new IllegalArgumentException("Last name cannot be empty");
        }
        
        if (email.trim().isEmpty() || !isValidEmail(email)) {
            throw new IllegalArgumentException("Email must be valid");
        }
        
        if (totalFlights < 0) {
            throw new IllegalArgumentException("Total flights cannot be negative");
        }
        
        // Normalize strings
        customerId = customerId.trim();
        firstName = firstName.trim();
        lastName = lastName.trim();
        email = email.trim().toLowerCase();
    }
    
    /**
     * Factory method to create CustomerInfo for a new customer.
     * 
     * @param customerId Customer ID
     * @param firstName First name
     * @param lastName Last name
     * @param email Email address
     * @return CustomerInfo for new customer with BRONZE loyalty level
     */
    public static CustomerInfo newCustomer(String customerId, String firstName, String lastName, String email) {
        return new CustomerInfo(customerId, firstName, lastName, email, LoyaltyLevel.BRONZE, 0, false);
    }
    
    /**
     * Factory method to create CustomerInfo for an existing customer.
     * 
     * @param customerId Customer ID
     * @param firstName First name
     * @param lastName Last name
     * @param email Email address
     * @param loyaltyLevel Current loyalty level
     * @param totalFlights Total flights taken
     * @return CustomerInfo instance
     */
    public static CustomerInfo existingCustomer(String customerId, String firstName, String lastName, 
                                              String email, LoyaltyLevel loyaltyLevel, int totalFlights) {
        boolean isFrequentFlyer = totalFlights >= 12 || loyaltyLevel.ordinal() >= LoyaltyLevel.GOLD.ordinal();
        return new CustomerInfo(customerId, firstName, lastName, email, loyaltyLevel, totalFlights, isFrequentFlyer);
    }
    
    /**
     * Gets the customer's full name.
     * 
     * @return Full name (first + last)
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    /**
     * Gets the loyalty discount percentage for this customer.
     * 
     * @return Discount percentage as decimal (0.0 to 1.0)
     */
    public double getLoyaltyDiscountPercentage() {
        return loyaltyLevel.getDiscountPercentage();
    }
    
    /**
     * Checks if customer qualifies for loyalty discounts.
     * 
     * @return true if customer has loyalty benefits
     */
    public boolean hasLoyaltyBenefits() {
        return loyaltyLevel != LoyaltyLevel.BRONZE || isFrequentFlyer;
    }
    
    /**
     * Checks if customer qualifies for premium services.
     * 
     * @return true if customer has premium status
     */
    public boolean hasPremiumStatus() {
        return loyaltyLevel.ordinal() >= LoyaltyLevel.GOLD.ordinal();
    }
    
    /**
     * Gets the priority level for this customer (higher number = higher priority).
     * 
     * @return Priority level (1-5)
     */
    public int getPriorityLevel() {
        return loyaltyLevel.getPriorityLevel();
    }
    
    /**
     * Checks if customer is eligible for early bird discounts.
     * Premium customers get better early bird deals.
     * 
     * @return true if eligible for enhanced early bird discounts
     */
    public boolean isEligibleForEnhancedEarlyBird() {
        return hasPremiumStatus() || totalFlights >= 20;
    }
    
    /**
     * Checks if customer is eligible for last-minute deals.
     * Frequent flyers often get access to better last-minute pricing.
     * 
     * @return true if eligible for enhanced last-minute deals
     */
    public boolean isEligibleForEnhancedLastMinute() {
        return isFrequentFlyer || loyaltyLevel.ordinal() >= LoyaltyLevel.SILVER.ordinal();
    }
    
    /**
     * Simple email validation.
     * 
     * @param email Email to validate
     * @return true if email format is valid
     */
    private boolean isValidEmail(String email) {
        return email != null && 
               email.contains("@") && 
               email.contains(".") && 
               email.length() > 5 &&
               !email.startsWith("@") &&
               !email.endsWith("@");
    }
    
    /**
     * Enumeration for customer loyalty levels.
     * Each level provides different benefits and discount percentages.
     */
    public enum LoyaltyLevel {
        BRONZE("Bronze", "Entry level", 0.0, 1, 0),
        SILVER("Silver", "Frequent traveler", 0.05, 2, 10),
        GOLD("Gold", "Premium member", 0.10, 3, 25),
        PLATINUM("Platinum", "Elite member", 0.15, 4, 50),
        DIAMOND("Diamond", "Top tier", 0.20, 5, 100);
        
        private final String displayName;
        private final String description;
        private final double discountPercentage;
        private final int priorityLevel;
        private final int minimumFlights;
        
        LoyaltyLevel(String displayName, String description, double discountPercentage, 
                    int priorityLevel, int minimumFlights) {
            this.displayName = displayName;
            this.description = description;
            this.discountPercentage = discountPercentage;
            this.priorityLevel = priorityLevel;
            this.minimumFlights = minimumFlights;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
        
        public double getDiscountPercentage() {
            return discountPercentage;
        }
        
        public int getPriorityLevel() {
            return priorityLevel;
        }
        
        public int getMinimumFlights() {
            return minimumFlights;
        }
        
        /**
         * Determines the appropriate loyalty level based on flight count.
         * 
         * @param flightCount Total flights taken
         * @return Appropriate loyalty level
         */
        public static LoyaltyLevel fromFlightCount(int flightCount) {
            if (flightCount >= DIAMOND.minimumFlights) return DIAMOND;
            if (flightCount >= PLATINUM.minimumFlights) return PLATINUM;
            if (flightCount >= GOLD.minimumFlights) return GOLD;
            if (flightCount >= SILVER.minimumFlights) return SILVER;
            return BRONZE;
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s %s - %s)", 
            customerId, firstName, lastName, loyaltyLevel.getDisplayName());
    }
}
