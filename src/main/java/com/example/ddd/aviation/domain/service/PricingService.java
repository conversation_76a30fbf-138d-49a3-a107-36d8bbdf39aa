package com.example.ddd.aviation.domain.service;

import com.example.ddd.aviation.domain.model.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Domain Service for flight ticket pricing calculations.
 * 
 * This domain service encapsulates the core business logic for calculating
 * ticket prices based on various factors including base price, applicable
 * pricing rules, customer information, and booking context.
 * 
 * Key Design Decisions:
 * - Domain service containing complex business logic that doesn't belong to a single entity
 * - Stateless service focusing on pricing calculations
 * - Uses other domain services for rule evaluation
 * - Returns immutable PricingCalculation value objects
 * - Handles rule conflicts and priorities
 * 
 * <AUTHOR> Aviation System
 */
public class PricingService {
    
    private final RuleEvaluationService ruleEvaluationService;
    
    /**
     * Creates a PricingService with required dependencies.
     * 
     * @param ruleEvaluationService Service for evaluating pricing rules
     */
    public PricingService(RuleEvaluationService ruleEvaluationService) {
        this.ruleEvaluationService = Objects.requireNonNull(ruleEvaluationService, 
            "Rule evaluation service cannot be null");
    }
    
    /**
     * Calculates the final ticket price for a flight and customer.
     * 
     * This is the main pricing method that:
     * 1. Starts with the flight's base price
     * 2. Evaluates all applicable pricing rules
     * 3. Applies rules in priority order
     * 4. Handles rule conflicts and combinations
     * 5. Returns a detailed pricing calculation
     * 
     * @param flight The flight to price
     * @param customer The customer information
     * @param bookingTime The time of booking
     * @return PricingCalculation with detailed breakdown
     */
    public PricingCalculation calculatePrice(Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(customer, "Customer cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        
        // Validate flight is bookable
        if (!flight.isBookable()) {
            throw new IllegalArgumentException("Flight is not available for booking: " + flight.getStatus());
        }
        
        // Start with base price
        Money basePrice = flight.getBasePrice();
        
        // Get all applicable rules
        List<PricingRule> applicableRules = ruleEvaluationService.getApplicableRules(flight, customer, bookingTime);
        
        // Apply rules in priority order (highest priority first)
        List<PricingRule> sortedRules = applicableRules.stream()
            .sorted((r1, r2) -> Integer.compare(r2.getPriority(), r1.getPriority()))
            .toList();
        
        return applyRulesSequentially(basePrice, sortedRules, flight, customer, bookingTime);
    }
    
    /**
     * Calculates a quick price estimate without detailed rule breakdown.
     * This is useful for search results where performance is critical.
     * 
     * @param flight The flight to price
     * @param customer The customer information
     * @return Estimated final price
     */
    public Money calculateQuickEstimate(Flight flight, CustomerInfo customer) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(customer, "Customer cannot be null");
        
        Money basePrice = flight.getBasePrice();
        
        // Apply quick estimates for common discounts
        Money estimatedPrice = basePrice;
        
        // Apply loyalty discount if applicable
        if (customer.hasLoyaltyBenefits()) {
            BigDecimal loyaltyDiscount = BigDecimal.valueOf(customer.getLoyaltyDiscountPercentage());
            estimatedPrice = estimatedPrice.applyDiscount(loyaltyDiscount);
        }
        
        // Apply demand-based pricing adjustment
        Flight.DemandLevel demandLevel = flight.getDemandLevel();
        BigDecimal demandMultiplier = BigDecimal.valueOf(1.0 + demandLevel.getPriceMultiplier());
        estimatedPrice = estimatedPrice.multiply(demandMultiplier);
        
        return estimatedPrice;
    }
    
    /**
     * Validates that a calculated price is reasonable and within business constraints.
     * 
     * @param originalPrice The original base price
     * @param finalPrice The calculated final price
     * @throws IllegalStateException if price is outside acceptable bounds
     */
    public void validatePriceReasonableness(Money originalPrice, Money finalPrice) {
        Objects.requireNonNull(originalPrice, "Original price cannot be null");
        Objects.requireNonNull(finalPrice, "Final price cannot be null");
        
        // Ensure final price is not negative
        if (finalPrice.isZero() || finalPrice.amount().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalStateException("Final price cannot be zero or negative");
        }
        
        // Ensure final price is not more than 200% of original (prevent extreme price increases)
        Money maxAllowedPrice = originalPrice.multiply(BigDecimal.valueOf(2.0));
        if (finalPrice.isGreaterThan(maxAllowedPrice)) {
            throw new IllegalStateException(
                String.format("Final price %s exceeds maximum allowed %s", finalPrice, maxAllowedPrice));
        }
        
        // Ensure final price is not less than 10% of original (prevent extreme discounts)
        Money minAllowedPrice = originalPrice.multiply(BigDecimal.valueOf(0.1));
        if (finalPrice.isLessThan(minAllowedPrice)) {
            throw new IllegalStateException(
                String.format("Final price %s is below minimum allowed %s", finalPrice, minAllowedPrice));
        }
    }
    
    /**
     * Calculates the total savings from all applied discounts.
     * 
     * @param basePrice The original base price
     * @param finalPrice The final calculated price
     * @return Money representing total savings
     */
    public Money calculateTotalSavings(Money basePrice, Money finalPrice) {
        Objects.requireNonNull(basePrice, "Base price cannot be null");
        Objects.requireNonNull(finalPrice, "Final price cannot be null");
        
        if (finalPrice.isGreaterThan(basePrice)) {
            return Money.zero(basePrice.currency());
        }
        
        return basePrice.subtract(finalPrice);
    }
    
    /**
     * Calculates the savings percentage.
     * 
     * @param basePrice The original base price
     * @param finalPrice The final calculated price
     * @return Savings percentage as decimal (0.0 to 1.0)
     */
    public BigDecimal calculateSavingsPercentage(Money basePrice, Money finalPrice) {
        Objects.requireNonNull(basePrice, "Base price cannot be null");
        Objects.requireNonNull(finalPrice, "Final price cannot be null");
        
        if (finalPrice.isGreaterThan(basePrice) || basePrice.isZero()) {
            return BigDecimal.ZERO;
        }
        
        Money savings = basePrice.subtract(finalPrice);
        return savings.amount().divide(basePrice.amount(), 4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * Applies pricing rules sequentially, handling conflicts and combinations.
     * 
     * @param basePrice Starting price
     * @param rules Rules to apply in order
     * @param flight Flight context
     * @param customer Customer context
     * @param bookingTime Booking context
     * @return PricingCalculation with detailed breakdown
     */
    private PricingCalculation applyRulesSequentially(Money basePrice, List<PricingRule> rules, 
                                                     Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        
        PricingCalculation.Builder calculationBuilder = PricingCalculation.builder()
            .withBasePrice(basePrice)
            .withFlight(flight)
            .withCustomer(customer)
            .withBookingTime(bookingTime);
        
        Money currentPrice = basePrice;
        
        for (PricingRule rule : rules) {
            try {
                Money priceBeforeRule = currentPrice;
                Money adjustment = rule.calculatePriceAdjustment(currentPrice);
                currentPrice = rule.applyRule(currentPrice);
                
                // Add rule application to calculation
                calculationBuilder.addAppliedRule(rule, priceBeforeRule, currentPrice, adjustment);
                
            } catch (Exception e) {
                // Log rule application failure but continue with other rules
                // In a real system, this would use proper logging
                System.err.println("Failed to apply rule " + rule.getRuleName() + ": " + e.getMessage());
            }
        }
        
        // Validate final price
        validatePriceReasonableness(basePrice, currentPrice);
        
        // Build final calculation
        Money totalSavings = calculateTotalSavings(basePrice, currentPrice);
        BigDecimal savingsPercentage = calculateSavingsPercentage(basePrice, currentPrice);
        
        return calculationBuilder
            .withFinalPrice(currentPrice)
            .withTotalSavings(totalSavings)
            .withSavingsPercentage(savingsPercentage)
            .build();
    }
    
    /**
     * Determines if two pricing rules can be combined or if they conflict.
     * 
     * @param rule1 First rule
     * @param rule2 Second rule
     * @return true if rules can be combined
     */
    public boolean canCombineRules(PricingRule rule1, PricingRule rule2) {
        Objects.requireNonNull(rule1, "Rule 1 cannot be null");
        Objects.requireNonNull(rule2, "Rule 2 cannot be null");
        
        // Same rule type usually can't be combined
        if (rule1.getRuleType() == rule2.getRuleType()) {
            return false;
        }
        
        // Dynamic pricing rules typically don't combine with discount rules
        if (rule1.getRuleType() == PricingRule.RuleType.DYNAMIC_PRICING ||
            rule2.getRuleType() == PricingRule.RuleType.DYNAMIC_PRICING) {
            return false;
        }
        
        // Check if rule conditions have conflicts
        return !rule1.getConditions().hasOverlapWith(rule2.getConditions());
    }
    
    /**
     * Gets the maximum discount percentage that can be applied to prevent extreme discounts.
     * 
     * @param customer Customer information
     * @return Maximum discount percentage
     */
    public BigDecimal getMaximumDiscountPercentage(CustomerInfo customer) {
        Objects.requireNonNull(customer, "Customer cannot be null");
        
        // Premium customers can get higher maximum discounts
        if (customer.hasPremiumStatus()) {
            return BigDecimal.valueOf(0.5); // 50% max for premium customers
        } else if (customer.hasLoyaltyBenefits()) {
            return BigDecimal.valueOf(0.3); // 30% max for loyalty members
        } else {
            return BigDecimal.valueOf(0.2); // 20% max for regular customers
        }
    }
}
