package com.example.ddd.aviation.domain.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Value Object representing the result of a pricing calculation.
 * 
 * This immutable value object encapsulates the complete breakdown of how
 * a ticket price was calculated, including base price, applied rules,
 * adjustments, and final price. It provides transparency into the pricing
 * process for both business users and customers.
 * 
 * Key Design Decisions:
 * - Immutable value object for thread safety
 * - Contains complete audit trail of pricing decisions
 * - Uses builder pattern for complex construction
 * - Provides methods for analysis and reporting
 * - Includes context information for debugging
 * 
 * <AUTHOR> Aviation System
 */
public record PricingCalculation(
    Money basePrice,
    Money finalPrice,
    Money totalSavings,
    BigDecimal savingsPercentage,
    Flight flight,
    CustomerInfo customer,
    LocalDateTime bookingTime,
    LocalDateTime calculationTime,
    List<RuleApplication> appliedRules
) {
    
    /**
     * Creates a PricingCalculation with validation.
     */
    public PricingCalculation {
        Objects.requireNonNull(basePrice, "Base price cannot be null");
        Objects.requireNonNull(finalPrice, "Final price cannot be null");
        Objects.requireNonNull(totalSavings, "Total savings cannot be null");
        Objects.requireNonNull(savingsPercentage, "Savings percentage cannot be null");
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(customer, "Customer cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        Objects.requireNonNull(calculationTime, "Calculation time cannot be null");
        Objects.requireNonNull(appliedRules, "Applied rules cannot be null");
        
        // Validate currency consistency
        if (!basePrice.currency().equals(finalPrice.currency()) ||
            !basePrice.currency().equals(totalSavings.currency())) {
            throw new IllegalArgumentException("All monetary amounts must use the same currency");
        }
        
        // Validate savings percentage
        if (savingsPercentage.compareTo(BigDecimal.ZERO) < 0 || 
            savingsPercentage.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("Savings percentage must be between 0.0 and 1.0");
        }
        
        // Make appliedRules immutable
        appliedRules = Collections.unmodifiableList(new ArrayList<>(appliedRules));
    }
    
    /**
     * Creates a builder for constructing PricingCalculation instances.
     * 
     * @return New PricingCalculation.Builder
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Gets the number of rules that were applied.
     * 
     * @return Number of applied rules
     */
    public int getAppliedRuleCount() {
        return appliedRules.size();
    }
    
    /**
     * Checks if any discounts were applied.
     * 
     * @return true if discounts were applied
     */
    public boolean hasDiscounts() {
        return !totalSavings.isZero();
    }
    
    /**
     * Checks if the price was increased (due to dynamic pricing).
     * 
     * @return true if final price is higher than base price
     */
    public boolean hasPriceIncrease() {
        return finalPrice.isGreaterThan(basePrice);
    }
    
    /**
     * Gets the price increase amount if applicable.
     * 
     * @return Money representing price increase, or zero if no increase
     */
    public Money getPriceIncrease() {
        if (hasPriceIncrease()) {
            return finalPrice.subtract(basePrice);
        }
        return Money.zero(basePrice.currency());
    }
    
    /**
     * Gets all discount rules that were applied.
     * 
     * @return List of discount rule applications
     */
    public List<RuleApplication> getDiscountRules() {
        return appliedRules.stream()
            .filter(ra -> ra.rule().getRuleType() != PricingRule.RuleType.DYNAMIC_PRICING)
            .filter(ra -> ra.priceAfter().isLessThan(ra.priceBefore()))
            .toList();
    }
    
    /**
     * Gets all dynamic pricing rules that were applied.
     * 
     * @return List of dynamic pricing rule applications
     */
    public List<RuleApplication> getDynamicPricingRules() {
        return appliedRules.stream()
            .filter(ra -> ra.rule().getRuleType() == PricingRule.RuleType.DYNAMIC_PRICING)
            .toList();
    }
    
    /**
     * Gets a summary of the pricing calculation for display purposes.
     * 
     * @return Human-readable pricing summary
     */
    public String getPricingSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("Base Price: %s%n", basePrice));
        
        if (hasDiscounts()) {
            summary.append(String.format("Total Savings: %s (%.1f%%)%n", 
                totalSavings, savingsPercentage.multiply(BigDecimal.valueOf(100))));
        }
        
        if (hasPriceIncrease()) {
            summary.append(String.format("Price Increase: %s%n", getPriceIncrease()));
        }
        
        summary.append(String.format("Final Price: %s%n", finalPrice));
        summary.append(String.format("Rules Applied: %d", getAppliedRuleCount()));
        
        return summary.toString();
    }
    
    /**
     * Gets a detailed breakdown of all applied rules.
     * 
     * @return Detailed rule breakdown
     */
    public String getDetailedBreakdown() {
        StringBuilder breakdown = new StringBuilder();
        breakdown.append(String.format("Pricing Calculation for Flight %s%n", flight.getFlightNumber()));
        breakdown.append(String.format("Customer: %s (%s)%n", 
            customer.getFullName(), customer.loyaltyLevel().getDisplayName()));
        breakdown.append(String.format("Booking Time: %s%n", bookingTime));
        breakdown.append("----------------------------------------%n");
        breakdown.append(String.format("Base Price: %s%n", basePrice));
        
        Money currentPrice = basePrice;
        for (RuleApplication ruleApp : appliedRules) {
            String changeType = ruleApp.priceAfter().isLessThan(ruleApp.priceBefore()) ? "Discount" : "Increase";
            breakdown.append(String.format("Applied %s: %s%n", changeType, ruleApp.rule().getRuleName()));
            breakdown.append(String.format("  Adjustment: %s%n", ruleApp.adjustment()));
            breakdown.append(String.format("  Price: %s → %s%n", ruleApp.priceBefore(), ruleApp.priceAfter()));
            currentPrice = ruleApp.priceAfter();
        }
        
        breakdown.append("----------------------------------------%n");
        breakdown.append(String.format("Final Price: %s%n", finalPrice));
        
        if (hasDiscounts()) {
            breakdown.append(String.format("Total Savings: %s (%.1f%%)%n", 
                totalSavings, savingsPercentage.multiply(BigDecimal.valueOf(100))));
        }
        
        return breakdown.toString();
    }
    
    /**
     * Record representing the application of a single pricing rule.
     */
    public record RuleApplication(
        PricingRule rule,
        Money priceBefore,
        Money priceAfter,
        Money adjustment
    ) {
        public RuleApplication {
            Objects.requireNonNull(rule, "Rule cannot be null");
            Objects.requireNonNull(priceBefore, "Price before cannot be null");
            Objects.requireNonNull(priceAfter, "Price after cannot be null");
            Objects.requireNonNull(adjustment, "Adjustment cannot be null");
        }
        
        /**
         * Gets the savings from this rule application.
         * 
         * @return Savings amount (positive for discounts, negative for increases)
         */
        public Money getSavings() {
            if (priceAfter.isLessThan(priceBefore)) {
                return priceBefore.subtract(priceAfter);
            }
            return Money.zero(priceBefore.currency());
        }
        
        /**
         * Checks if this rule application resulted in a discount.
         * 
         * @return true if price was reduced
         */
        public boolean isDiscount() {
            return priceAfter.isLessThan(priceBefore);
        }
        
        /**
         * Checks if this rule application resulted in a price increase.
         * 
         * @return true if price was increased
         */
        public boolean isPriceIncrease() {
            return priceAfter.isGreaterThan(priceBefore);
        }
    }
    
    /**
     * Builder class for constructing PricingCalculation instances.
     */
    public static class Builder {
        private Money basePrice;
        private Money finalPrice;
        private Money totalSavings;
        private BigDecimal savingsPercentage;
        private Flight flight;
        private CustomerInfo customer;
        private LocalDateTime bookingTime;
        private LocalDateTime calculationTime;
        private final List<RuleApplication> appliedRules = new ArrayList<>();
        
        private Builder() {
            this.calculationTime = LocalDateTime.now();
        }
        
        public Builder withBasePrice(Money basePrice) {
            this.basePrice = basePrice;
            return this;
        }
        
        public Builder withFinalPrice(Money finalPrice) {
            this.finalPrice = finalPrice;
            return this;
        }
        
        public Builder withTotalSavings(Money totalSavings) {
            this.totalSavings = totalSavings;
            return this;
        }
        
        public Builder withSavingsPercentage(BigDecimal savingsPercentage) {
            this.savingsPercentage = savingsPercentage;
            return this;
        }
        
        public Builder withFlight(Flight flight) {
            this.flight = flight;
            return this;
        }
        
        public Builder withCustomer(CustomerInfo customer) {
            this.customer = customer;
            return this;
        }
        
        public Builder withBookingTime(LocalDateTime bookingTime) {
            this.bookingTime = bookingTime;
            return this;
        }
        
        public Builder withCalculationTime(LocalDateTime calculationTime) {
            this.calculationTime = calculationTime;
            return this;
        }
        
        public Builder addAppliedRule(PricingRule rule, Money priceBefore, Money priceAfter, Money adjustment) {
            this.appliedRules.add(new RuleApplication(rule, priceBefore, priceAfter, adjustment));
            return this;
        }
        
        public PricingCalculation build() {
            return new PricingCalculation(
                basePrice, finalPrice, totalSavings, savingsPercentage,
                flight, customer, bookingTime, calculationTime, appliedRules
            );
        }
    }
    
    @Override
    public String toString() {
        return String.format("PricingCalculation{%s → %s, %d rules applied, %.1f%% savings}", 
            basePrice, finalPrice, getAppliedRuleCount(), 
            savingsPercentage.multiply(BigDecimal.valueOf(100)));
    }
}
