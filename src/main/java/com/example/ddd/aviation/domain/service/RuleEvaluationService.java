package com.example.ddd.aviation.domain.service;

import com.example.ddd.aviation.domain.model.*;
import com.example.ddd.aviation.domain.repository.PricingRuleRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Domain Service for evaluating which pricing rules apply to a given context.
 * 
 * This domain service encapsulates the logic for determining which pricing
 * rules should be applied to a flight booking based on flight characteristics,
 * customer information, and booking context. It handles rule filtering,
 * conflict resolution, and rule prioritization.
 * 
 * Key Design Decisions:
 * - Domain service for complex rule evaluation logic
 * - Stateless service that can be safely shared
 * - Uses repository to access pricing rules
 * - Handles rule conflicts and combinations
 * - Provides different evaluation strategies
 * 
 * <AUTHOR> Aviation System
 */
public class RuleEvaluationService {
    
    private final PricingRuleRepository pricingRuleRepository;
    
    /**
     * Creates a RuleEvaluationService with required dependencies.
     * 
     * @param pricingRuleRepository Repository for accessing pricing rules
     */
    public RuleEvaluationService(PricingRuleRepository pricingRuleRepository) {
        this.pricingRuleRepository = Objects.requireNonNull(pricingRuleRepository, 
            "Pricing rule repository cannot be null");
    }
    
    /**
     * Gets all pricing rules that apply to the given context.
     * 
     * This method:
     * 1. Retrieves all active pricing rules
     * 2. Filters rules based on their conditions
     * 3. Resolves conflicts between rules
     * 4. Returns applicable rules in priority order
     * 
     * @param flight The flight context
     * @param customer The customer context
     * @param bookingTime The booking time context
     * @return List of applicable pricing rules
     */
    public List<PricingRule> getApplicableRules(Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(customer, "Customer cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        
        // Get all active rules
        List<PricingRule> allActiveRules = pricingRuleRepository.findActiveRules();
        
        // Filter rules that apply to this context
        List<PricingRule> applicableRules = allActiveRules.stream()
            .filter(rule -> rule.appliesTo(flight, customer, bookingTime))
            .collect(Collectors.toList());
        
        // Resolve conflicts and return final set
        return resolveRuleConflicts(applicableRules, flight, customer, bookingTime);
    }
    
    /**
     * Gets applicable rules for a specific rule type.
     * 
     * @param flight The flight context
     * @param customer The customer context
     * @param bookingTime The booking time context
     * @param ruleType The specific rule type to filter for
     * @return List of applicable rules of the specified type
     */
    public List<PricingRule> getApplicableRulesByType(Flight flight, CustomerInfo customer, 
                                                     LocalDateTime bookingTime, PricingRule.RuleType ruleType) {
        Objects.requireNonNull(ruleType, "Rule type cannot be null");
        
        return getApplicableRules(flight, customer, bookingTime).stream()
            .filter(rule -> rule.getRuleType() == ruleType)
            .toList();
    }
    
    /**
     * Checks if any loyalty discount rules apply to the customer.
     * 
     * @param customer The customer to check
     * @return true if customer qualifies for loyalty discounts
     */
    public boolean hasApplicableLoyaltyDiscounts(CustomerInfo customer) {
        Objects.requireNonNull(customer, "Customer cannot be null");
        
        List<PricingRule> loyaltyRules = pricingRuleRepository.findByRuleType(PricingRule.RuleType.LOYALTY_DISCOUNT);
        
        return loyaltyRules.stream()
            .filter(PricingRule::isActive)
            .anyMatch(rule -> {
                // Create a dummy context for evaluation
                // In a real system, this might be optimized differently
                try {
                    return rule.getConditions().minimumLoyaltyLevel()
                        .map(minLevel -> customer.loyaltyLevel().ordinal() >= minLevel.ordinal())
                        .orElse(false);
                } catch (Exception e) {
                    return false;
                }
            });
    }
    
    /**
     * Checks if any seasonal discounts apply to the flight.
     * 
     * @param flight The flight to check
     * @return true if flight qualifies for seasonal discounts
     */
    public boolean hasApplicableSeasonalDiscounts(Flight flight) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        
        FlightSchedule.Season flightSeason = flight.getSchedule().getSeason();
        List<PricingRule> seasonalRules = pricingRuleRepository.findByRuleType(PricingRule.RuleType.SEASONAL_DISCOUNT);
        
        return seasonalRules.stream()
            .filter(PricingRule::isActive)
            .anyMatch(rule -> rule.getConditions().targetSeason()
                .map(targetSeason -> targetSeason == flightSeason)
                .orElse(false));
    }
    
    /**
     * Checks if early bird discounts apply to the booking.
     * 
     * @param flight The flight
     * @param bookingTime The booking time
     * @return true if booking qualifies for early bird discounts
     */
    public boolean hasApplicableEarlyBirdDiscounts(Flight flight, LocalDateTime bookingTime) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        
        long daysUntilDeparture = flight.getSchedule().getDaysUntilDeparture();
        List<PricingRule> earlyBirdRules = pricingRuleRepository.findByRuleType(PricingRule.RuleType.EARLY_BIRD_DISCOUNT);
        
        return earlyBirdRules.stream()
            .filter(PricingRule::isActive)
            .anyMatch(rule -> rule.getConditions().minimumDaysInAdvance()
                .map(minDays -> daysUntilDeparture >= minDays)
                .orElse(false));
    }
    
    /**
     * Checks if last-minute deals apply to the booking.
     * 
     * @param flight The flight
     * @param bookingTime The booking time
     * @return true if booking qualifies for last-minute deals
     */
    public boolean hasApplicableLastMinuteDeals(Flight flight, LocalDateTime bookingTime) {
        Objects.requireNonNull(flight, "Flight cannot be null");
        Objects.requireNonNull(bookingTime, "Booking time cannot be null");
        
        long hoursUntilDeparture = flight.getSchedule().getHoursUntilDeparture();
        List<PricingRule> lastMinuteRules = pricingRuleRepository.findByRuleType(PricingRule.RuleType.LAST_MINUTE_DEAL);
        
        return lastMinuteRules.stream()
            .filter(PricingRule::isActive)
            .anyMatch(rule -> rule.getConditions().maximumHoursBeforeDeparture()
                .map(maxHours -> hoursUntilDeparture <= maxHours)
                .orElse(false));
    }
    
    /**
     * Gets the best available discount for a customer on a specific flight.
     * This method evaluates all applicable discount rules and returns the one
     * that provides the maximum savings.
     * 
     * @param flight The flight
     * @param customer The customer
     * @param bookingTime The booking time
     * @return The best discount rule, or null if none apply
     */
    public PricingRule getBestAvailableDiscount(Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        List<PricingRule> discountRules = getApplicableRules(flight, customer, bookingTime).stream()
            .filter(rule -> rule.getRuleType() != PricingRule.RuleType.DYNAMIC_PRICING)
            .toList();
        
        return discountRules.stream()
            .max((r1, r2) -> r1.getDiscountPercentage().compareTo(r2.getDiscountPercentage()))
            .orElse(null);
    }
    
    /**
     * Resolves conflicts between pricing rules.
     * 
     * This method handles situations where multiple rules might conflict
     * or where business logic dictates that certain combinations should
     * not be applied together.
     * 
     * @param rules The list of potentially conflicting rules
     * @param flight Flight context for conflict resolution
     * @param customer Customer context for conflict resolution
     * @param bookingTime Booking context for conflict resolution
     * @return Resolved list of rules to apply
     */
    private List<PricingRule> resolveRuleConflicts(List<PricingRule> rules, Flight flight, 
                                                  CustomerInfo customer, LocalDateTime bookingTime) {
        
        // Group rules by type to handle conflicts
        var rulesByType = rules.stream()
            .collect(Collectors.groupingBy(PricingRule::getRuleType));
        
        List<PricingRule> resolvedRules = rules.stream().collect(Collectors.toList());
        
        // Handle early bird vs last-minute conflicts
        if (rulesByType.containsKey(PricingRule.RuleType.EARLY_BIRD_DISCOUNT) &&
            rulesByType.containsKey(PricingRule.RuleType.LAST_MINUTE_DEAL)) {
            
            // These are mutually exclusive - keep the one with higher discount
            List<PricingRule> earlyBirdRules = rulesByType.get(PricingRule.RuleType.EARLY_BIRD_DISCOUNT);
            List<PricingRule> lastMinuteRules = rulesByType.get(PricingRule.RuleType.LAST_MINUTE_DEAL);
            
            PricingRule bestEarlyBird = earlyBirdRules.stream()
                .max((r1, r2) -> r1.getDiscountPercentage().compareTo(r2.getDiscountPercentage()))
                .orElse(null);
            
            PricingRule bestLastMinute = lastMinuteRules.stream()
                .max((r1, r2) -> r1.getDiscountPercentage().compareTo(r2.getDiscountPercentage()))
                .orElse(null);
            
            if (bestEarlyBird != null && bestLastMinute != null) {
                if (bestEarlyBird.getDiscountPercentage().compareTo(bestLastMinute.getDiscountPercentage()) >= 0) {
                    resolvedRules.removeAll(lastMinuteRules);
                } else {
                    resolvedRules.removeAll(earlyBirdRules);
                }
            }
        }
        
        // Handle multiple rules of the same type - keep only the best one
        for (PricingRule.RuleType ruleType : rulesByType.keySet()) {
            List<PricingRule> rulesOfType = rulesByType.get(ruleType);
            if (rulesOfType.size() > 1) {
                // Keep only the rule with the highest discount/adjustment
                PricingRule bestRule = rulesOfType.stream()
                    .max((r1, r2) -> r1.getDiscountPercentage().compareTo(r2.getDiscountPercentage()))
                    .orElse(null);
                
                if (bestRule != null) {
                    resolvedRules.removeAll(rulesOfType);
                    resolvedRules.add(bestRule);
                }
            }
        }
        
        return resolvedRules;
    }
    
    /**
     * Validates that a set of rules can be applied together.
     * 
     * @param rules Rules to validate
     * @return true if rules can be combined
     */
    public boolean canApplyRulesTogether(List<PricingRule> rules) {
        Objects.requireNonNull(rules, "Rules cannot be null");
        
        if (rules.size() <= 1) {
            return true;
        }
        
        // Check for conflicting rule types
        long dynamicPricingCount = rules.stream()
            .filter(rule -> rule.getRuleType() == PricingRule.RuleType.DYNAMIC_PRICING)
            .count();
        
        // Only one dynamic pricing rule should be applied
        if (dynamicPricingCount > 1) {
            return false;
        }
        
        // Check for early bird + last minute conflict
        boolean hasEarlyBird = rules.stream()
            .anyMatch(rule -> rule.getRuleType() == PricingRule.RuleType.EARLY_BIRD_DISCOUNT);
        boolean hasLastMinute = rules.stream()
            .anyMatch(rule -> rule.getRuleType() == PricingRule.RuleType.LAST_MINUTE_DEAL);
        
        if (hasEarlyBird && hasLastMinute) {
            return false;
        }
        
        return true;
    }
}
