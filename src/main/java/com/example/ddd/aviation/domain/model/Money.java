package com.example.ddd.aviation.domain.model;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Currency;
import java.util.Objects;

/**
 * Value Object representing monetary amounts in the aviation domain.
 * 
 * This immutable value object encapsulates both the amount and currency,
 * providing type safety and business operations for monetary calculations.
 * Following DDD principles, it contains business logic related to money operations.
 * 
 * Key Design Decisions:
 * - Uses BigDecimal for precision in financial calculations
 * - Immutable to prevent accidental modifications
 * - Contains business logic for monetary operations (add, subtract, multiply)
 * - Validates currency compatibility in operations
 * 
 * <AUTHOR> Aviation System
 */
public record Money(BigDecimal amount, Currency currency) {
    
    /**
     * Creates a Money instance with validation.
     * 
     * @param amount The monetary amount (cannot be null)
     * @param currency The currency (cannot be null)
     * @throws IllegalArgumentException if amount or currency is null, or amount is negative
     */
    public Money {
        Objects.requireNonNull(amount, "Amount cannot be null");
        Objects.requireNonNull(currency, "Currency cannot be null");
        
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Amount cannot be negative");
        }
        
        // Scale to currency's default fraction digits for consistency
        amount = amount.setScale(currency.getDefaultFractionDigits(), RoundingMode.HALF_UP);
    }
    
    /**
     * Factory method to create Money in USD.
     * 
     * @param amount The amount in USD
     * @return Money instance in USD
     */
    public static Money usd(BigDecimal amount) {
        return new Money(amount, Currency.getInstance("USD"));
    }
    
    /**
     * Factory method to create Money in USD from double.
     * 
     * @param amount The amount in USD
     * @return Money instance in USD
     */
    public static Money usd(double amount) {
        return new Money(BigDecimal.valueOf(amount), Currency.getInstance("USD"));
    }
    
    /**
     * Factory method to create zero amount in given currency.
     * 
     * @param currency The currency
     * @return Money instance with zero amount
     */
    public static Money zero(Currency currency) {
        return new Money(BigDecimal.ZERO, currency);
    }
    
    /**
     * Adds another Money amount to this one.
     * Both amounts must be in the same currency.
     * 
     * @param other The Money to add
     * @return New Money instance with the sum
     * @throws IllegalArgumentException if currencies don't match
     */
    public Money add(Money other) {
        validateSameCurrency(other);
        return new Money(this.amount.add(other.amount), this.currency);
    }
    
    /**
     * Subtracts another Money amount from this one.
     * Both amounts must be in the same currency.
     * 
     * @param other The Money to subtract
     * @return New Money instance with the difference
     * @throws IllegalArgumentException if currencies don't match or result would be negative
     */
    public Money subtract(Money other) {
        validateSameCurrency(other);
        BigDecimal result = this.amount.subtract(other.amount);
        
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Subtraction would result in negative amount");
        }
        
        return new Money(result, this.currency);
    }
    
    /**
     * Multiplies this Money by a factor.
     * 
     * @param factor The multiplication factor
     * @return New Money instance with the product
     * @throws IllegalArgumentException if factor is negative
     */
    public Money multiply(BigDecimal factor) {
        Objects.requireNonNull(factor, "Factor cannot be null");
        
        if (factor.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Factor cannot be negative");
        }
        
        BigDecimal result = this.amount.multiply(factor);
        return new Money(result, this.currency);
    }
    
    /**
     * Multiplies this Money by a percentage (0.0 to 1.0).
     * 
     * @param percentage The percentage as decimal (e.g., 0.1 for 10%)
     * @return New Money instance with the calculated amount
     */
    public Money multiplyByPercentage(BigDecimal percentage) {
        return multiply(percentage);
    }
    
    /**
     * Applies a discount percentage to this Money.
     * 
     * @param discountPercentage The discount percentage (0.0 to 1.0)
     * @return New Money instance with discount applied
     */
    public Money applyDiscount(BigDecimal discountPercentage) {
        Objects.requireNonNull(discountPercentage, "Discount percentage cannot be null");
        
        if (discountPercentage.compareTo(BigDecimal.ZERO) < 0 || 
            discountPercentage.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("Discount percentage must be between 0.0 and 1.0");
        }
        
        BigDecimal discountAmount = this.amount.multiply(discountPercentage);
        return subtract(new Money(discountAmount, this.currency));
    }
    
    /**
     * Checks if this Money is greater than another.
     * 
     * @param other The Money to compare with
     * @return true if this amount is greater
     * @throws IllegalArgumentException if currencies don't match
     */
    public boolean isGreaterThan(Money other) {
        validateSameCurrency(other);
        return this.amount.compareTo(other.amount) > 0;
    }
    
    /**
     * Checks if this Money is less than another.
     * 
     * @param other The Money to compare with
     * @return true if this amount is less
     * @throws IllegalArgumentException if currencies don't match
     */
    public boolean isLessThan(Money other) {
        validateSameCurrency(other);
        return this.amount.compareTo(other.amount) < 0;
    }
    
    /**
     * Checks if this Money is zero.
     * 
     * @return true if amount is zero
     */
    public boolean isZero() {
        return this.amount.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * Validates that another Money has the same currency as this one.
     * 
     * @param other The Money to validate
     * @throws IllegalArgumentException if currencies don't match
     */
    private void validateSameCurrency(Money other) {
        Objects.requireNonNull(other, "Other money cannot be null");
        
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException(
                String.format("Currency mismatch: %s vs %s", 
                    this.currency.getCurrencyCode(), 
                    other.currency.getCurrencyCode())
            );
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s %s", currency.getCurrencyCode(), amount);
    }
}
