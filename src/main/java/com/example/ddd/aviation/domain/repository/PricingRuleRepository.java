package com.example.ddd.aviation.domain.repository;

import com.example.ddd.aviation.domain.model.PricingRule;
import com.example.ddd.aviation.domain.model.CustomerInfo;
import com.example.ddd.aviation.domain.model.FlightSchedule;
import com.example.ddd.aviation.domain.model.Flight;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for PricingRule entities.
 * 
 * This interface defines the contract for persisting and retrieving PricingRule
 * entities. It provides domain-focused query methods for finding rules based on
 * business criteria such as rule type, conditions, and activation status.
 * 
 * Key Design Decisions:
 * - Domain-focused query methods for business scenarios
 * - Returns domain objects with full business logic
 * - Supports rule filtering by various business criteria
 * - Provides methods for rule management operations
 * - Includes performance-optimized queries for pricing calculations
 * 
 * <AUTHOR> Aviation System
 */
public interface PricingRuleRepository {
    
    /**
     * Saves a pricing rule entity.
     * 
     * @param pricingRule The pricing rule to save
     * @return The saved pricing rule
     */
    PricingRule save(PricingRule pricingRule);
    
    /**
     * Finds a pricing rule by its unique identifier.
     * 
     * @param ruleId The rule ID
     * @return Optional containing the rule if found
     */
    Optional<PricingRule> findById(String ruleId);
    
    /**
     * Finds a pricing rule by its name.
     * Rule names should be unique within the system.
     * 
     * @param ruleName The rule name
     * @return Optional containing the rule if found
     */
    Optional<PricingRule> findByRuleName(String ruleName);
    
    /**
     * Finds all active pricing rules.
     * This is the most commonly used query for pricing calculations.
     * 
     * @return List of active pricing rules
     */
    List<PricingRule> findActiveRules();
    
    /**
     * Finds all inactive pricing rules.
     * 
     * @return List of inactive pricing rules
     */
    List<PricingRule> findInactiveRules();
    
    /**
     * Finds all pricing rules regardless of status.
     * 
     * @return List of all pricing rules
     */
    List<PricingRule> findAll();
    
    /**
     * Finds pricing rules by rule type.
     * 
     * @param ruleType The rule type to filter by
     * @return List of rules of the specified type
     */
    List<PricingRule> findByRuleType(PricingRule.RuleType ruleType);
    
    /**
     * Finds active pricing rules by rule type.
     * 
     * @param ruleType The rule type to filter by
     * @return List of active rules of the specified type
     */
    List<PricingRule> findActiveRulesByType(PricingRule.RuleType ruleType);
    
    /**
     * Finds pricing rules that target a specific loyalty level.
     * 
     * @param loyaltyLevel The loyalty level
     * @return List of rules targeting the loyalty level
     */
    List<PricingRule> findByTargetLoyaltyLevel(CustomerInfo.LoyaltyLevel loyaltyLevel);
    
    /**
     * Finds pricing rules that apply to a specific season.
     * 
     * @param season The season
     * @return List of rules for the specified season
     */
    List<PricingRule> findByTargetSeason(FlightSchedule.Season season);
    
    /**
     * Finds pricing rules that apply to a specific demand level.
     * 
     * @param demandLevel The demand level
     * @return List of rules for the specified demand level
     */
    List<PricingRule> findByTargetDemandLevel(Flight.DemandLevel demandLevel);
    
    /**
     * Finds pricing rules with priority greater than or equal to the specified value.
     * 
     * @param minimumPriority The minimum priority level
     * @return List of rules with sufficient priority
     */
    List<PricingRule> findByPriorityGreaterThanEqual(int minimumPriority);
    
    /**
     * Finds pricing rules ordered by priority (highest first).
     * 
     * @return List of rules ordered by priority
     */
    List<PricingRule> findAllOrderByPriorityDesc();
    
    /**
     * Finds active pricing rules ordered by priority (highest first).
     * 
     * @return List of active rules ordered by priority
     */
    List<PricingRule> findActiveRulesOrderByPriorityDesc();
    
    /**
     * Finds early bird discount rules with minimum days greater than or equal to specified value.
     * 
     * @param minimumDays The minimum days in advance
     * @return List of early bird rules
     */
    List<PricingRule> findEarlyBirdRulesWithMinimumDays(int minimumDays);
    
    /**
     * Finds last-minute deal rules with maximum hours less than or equal to specified value.
     * 
     * @param maximumHours The maximum hours before departure
     * @return List of last-minute rules
     */
    List<PricingRule> findLastMinuteRulesWithMaximumHours(int maximumHours);
    
    /**
     * Finds rules that require frequent flyer status.
     * 
     * @return List of rules requiring frequent flyer status
     */
    List<PricingRule> findRulesRequiringFrequentFlyer();
    
    /**
     * Finds rules that apply only to domestic flights.
     * 
     * @return List of domestic-only rules
     */
    List<PricingRule> findDomesticOnlyRules();
    
    /**
     * Finds rules that apply only to international flights.
     * 
     * @return List of international-only rules
     */
    List<PricingRule> findInternationalOnlyRules();
    
    /**
     * Finds rules with discount percentage greater than or equal to specified value.
     * 
     * @param minimumDiscount The minimum discount percentage
     * @return List of rules with sufficient discount
     */
    List<PricingRule> findByDiscountPercentageGreaterThanEqual(double minimumDiscount);
    
    /**
     * Finds the top N rules by discount percentage.
     * 
     * @param limit The maximum number of rules to return
     * @return List of top discount rules
     */
    List<PricingRule> findTopDiscountRules(int limit);
    
    /**
     * Finds rules created after the specified date.
     * 
     * @param date The cutoff date
     * @return List of recently created rules
     */
    List<PricingRule> findRulesCreatedAfter(java.time.LocalDateTime date);
    
    /**
     * Finds rules updated after the specified date.
     * 
     * @param date The cutoff date
     * @return List of recently updated rules
     */
    List<PricingRule> findRulesUpdatedAfter(java.time.LocalDateTime date);
    
    /**
     * Counts the total number of pricing rules.
     * 
     * @return Total rule count
     */
    long count();
    
    /**
     * Counts active pricing rules.
     * 
     * @return Number of active rules
     */
    long countActiveRules();
    
    /**
     * Counts pricing rules by type.
     * 
     * @param ruleType The rule type
     * @return Number of rules of the specified type
     */
    long countByRuleType(PricingRule.RuleType ruleType);
    
    /**
     * Counts active pricing rules by type.
     * 
     * @param ruleType The rule type
     * @return Number of active rules of the specified type
     */
    long countActiveRulesByType(PricingRule.RuleType ruleType);
    
    /**
     * Checks if an active rule exists with the given name.
     * 
     * @param ruleName The rule name
     * @return true if an active rule with the name exists
     */
    boolean existsActiveRuleByName(String ruleName);
    
    /**
     * Checks if any active loyalty rules exist for the specified level.
     * 
     * @param loyaltyLevel The loyalty level
     * @return true if active loyalty rules exist
     */
    boolean existsActiveLoyaltyRulesForLevel(CustomerInfo.LoyaltyLevel loyaltyLevel);
    
    /**
     * Checks if any active seasonal rules exist for the specified season.
     * 
     * @param season The season
     * @return true if active seasonal rules exist
     */
    boolean existsActiveSeasonalRulesForSeason(FlightSchedule.Season season);
    
    /**
     * Deletes a pricing rule by its ID.
     * Note: This should be used carefully as it may affect pricing calculations.
     * 
     * @param ruleId The rule ID to delete
     */
    void deleteById(String ruleId);
    
    /**
     * Deletes all inactive pricing rules.
     * This is useful for cleanup operations.
     */
    void deleteInactiveRules();
    
    /**
     * Deletes all pricing rules.
     * Note: This is typically used only in testing scenarios.
     */
    void deleteAll();
}
