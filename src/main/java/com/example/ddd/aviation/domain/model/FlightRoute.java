package com.example.ddd.aviation.domain.model;

import java.util.Objects;

/**
 * Value Object representing a flight route in the aviation domain.
 * 
 * This immutable value object encapsulates the origin and destination airports
 * for a flight route. It provides validation and business logic related to
 * flight routing.
 * 
 * Key Design Decisions:
 * - Immutable to ensure data integrity
 * - Uses IATA airport codes for standardization
 * - Validates airport codes format and business rules
 * - Contains route-specific business logic
 * 
 * <AUTHOR> Aviation System
 */
public record FlightRoute(String origin, String destination) {
    
    /**
     * Creates a FlightRoute with validation.
     * 
     * @param origin The origin airport IATA code (3 letters)
     * @param destination The destination airport IATA code (3 letters)
     * @throws IllegalArgumentException if airport codes are invalid or same
     */
    public FlightRoute {
        Objects.requireNonNull(origin, "Origin airport cannot be null");
        Objects.requireNonNull(destination, "Destination airport cannot be null");
        
        validateAirportCode(origin, "Origin");
        validateAirportCode(destination, "Destination");
        
        if (origin.equals(destination)) {
            throw new IllegalArgumentException("Origin and destination cannot be the same");
        }
        
        // Normalize to uppercase for consistency
        origin = origin.toUpperCase().trim();
        destination = destination.toUpperCase().trim();
    }
    
    /**
     * Factory method to create a FlightRoute.
     * 
     * @param origin The origin airport code
     * @param destination The destination airport code
     * @return FlightRoute instance
     */
    public static FlightRoute of(String origin, String destination) {
        return new FlightRoute(origin, destination);
    }
    
    /**
     * Creates the reverse route (destination to origin).
     * 
     * @return FlightRoute with swapped origin and destination
     */
    public FlightRoute reverse() {
        return new FlightRoute(destination, origin);
    }
    
    /**
     * Checks if this route is domestic (within the same country).
     * This is a simplified implementation based on common airport code patterns.
     * In a real system, this would use a comprehensive airport database.
     * 
     * @return true if likely domestic route
     */
    public boolean isDomestic() {
        // Simplified logic: US domestic routes (starts with K or common US codes)
        // In production, this would use a proper airport database
        return isUSAirport(origin) && isUSAirport(destination);
    }
    
    /**
     * Checks if this route is international.
     * 
     * @return true if international route
     */
    public boolean isInternational() {
        return !isDomestic();
    }
    
    /**
     * Gets the route distance category for pricing purposes.
     * This is a simplified categorization for demonstration.
     * 
     * @return RouteDistance category
     */
    public RouteDistance getDistanceCategory() {
        // Simplified logic based on common route patterns
        // In production, this would calculate actual distances
        if (isDomestic()) {
            return RouteDistance.SHORT_HAUL;
        } else {
            // Simple heuristic: routes between North America and Europe/Asia are long-haul
            if ((isNorthAmericanAirport(origin) && (isEuropeanAirport(destination) || isAsianAirport(destination))) ||
                (isNorthAmericanAirport(destination) && (isEuropeanAirport(origin) || isAsianAirport(origin)))) {
                return RouteDistance.LONG_HAUL;
            }
            return RouteDistance.MEDIUM_HAUL;
        }
    }
    
    /**
     * Validates an airport code format.
     * 
     * @param code The airport code to validate
     * @param fieldName The field name for error messages
     * @throws IllegalArgumentException if code is invalid
     */
    private void validateAirportCode(String code, String fieldName) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + " airport code cannot be empty");
        }
        
        String trimmedCode = code.trim();
        if (trimmedCode.length() != 3) {
            throw new IllegalArgumentException(fieldName + " airport code must be exactly 3 characters");
        }
        
        if (!trimmedCode.matches("[A-Za-z]{3}")) {
            throw new IllegalArgumentException(fieldName + " airport code must contain only letters");
        }
    }
    
    /**
     * Simplified check for US airports.
     * In production, this would use a comprehensive airport database.
     */
    private boolean isUSAirport(String code) {
        // Simplified logic: many US airports start with K or are common codes
        return code.startsWith("K") || 
               "LAX,JFK,ORD,DFW,DEN,SFO,SEA,LAS,PHX,IAH,CLT,MIA,BOS,MSP,DTW,PHL,LGA,BWI,SLC,DCA".contains(code);
    }
    
    /**
     * Simplified check for North American airports.
     */
    private boolean isNorthAmericanAirport(String code) {
        return isUSAirport(code) || 
               "YYZ,YVR,YUL,YYC,YOW,YHZ".contains(code); // Common Canadian airports
    }
    
    /**
     * Simplified check for European airports.
     */
    private boolean isEuropeanAirport(String code) {
        return "LHR,CDG,FRA,AMS,MAD,FCO,MUC,ZUR,VIE,CPH,ARN,OSL,HEL,DUB,BRU".contains(code);
    }
    
    /**
     * Simplified check for Asian airports.
     */
    private boolean isAsianAirport(String code) {
        return "NRT,HND,ICN,PVG,PEK,HKG,SIN,BKK,KUL,DEL,BOM,DXB,DOH".contains(code);
    }
    
    /**
     * Enumeration for route distance categories.
     */
    public enum RouteDistance {
        SHORT_HAUL("Short Haul", "Domestic or regional flights under 3 hours"),
        MEDIUM_HAUL("Medium Haul", "International flights 3-6 hours"),
        LONG_HAUL("Long Haul", "Intercontinental flights over 6 hours");
        
        private final String displayName;
        private final String description;
        
        RouteDistance(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s → %s", origin, destination);
    }
}
