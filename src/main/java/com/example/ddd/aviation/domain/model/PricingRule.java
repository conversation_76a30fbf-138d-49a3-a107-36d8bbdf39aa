package com.example.ddd.aviation.domain.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * PricingRule Entity representing business rules for ticket pricing.
 * 
 * This entity encapsulates the various pricing rules that can be applied
 * to flight tickets, such as seasonal discounts, loyalty benefits, early bird
 * specials, and dynamic pricing based on demand.
 * 
 * Key Design Decisions:
 * - Entity with unique identity and lifecycle
 * - Encapsulates rule evaluation logic
 * - Uses strategy pattern through RuleType enum
 * - Immutable rule conditions once created
 * - Supports rule activation/deactivation
 * 
 * <AUTHOR> Aviation System
 */
public class PricingRule {
    
    private final String ruleId;
    private final String ruleName;
    private final RuleType ruleType;
    private final String description;
    private final BigDecimal discountPercentage;
    private final RuleConditions conditions;
    private boolean isActive;
    private final int priority;
    private final LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * Creates a new PricingRule.
     * 
     * @param ruleId Unique rule identifier
     * @param ruleName Human-readable rule name
     * @param ruleType Type of pricing rule
     * @param description Rule description
     * @param discountPercentage Discount percentage (0.0 to 1.0)
     * @param conditions Rule conditions
     * @param priority Rule priority (higher number = higher priority)
     */
    public PricingRule(String ruleId, String ruleName, RuleType ruleType, String description,
                       BigDecimal discountPercentage, RuleConditions conditions, int priority) {
        this.ruleId = validateRuleId(ruleId);
        this.ruleName = validateRuleName(ruleName);
        this.ruleType = Objects.requireNonNull(ruleType, "Rule type cannot be null");
        this.description = validateDescription(description);
        this.discountPercentage = validateDiscountPercentage(discountPercentage);
        this.conditions = Objects.requireNonNull(conditions, "Conditions cannot be null");
        this.priority = validatePriority(priority);
        this.isActive = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Factory method to create a seasonal discount rule.
     * 
     * @param ruleName Rule name
     * @param season Target season
     * @param discountPercentage Discount percentage
     * @return PricingRule for seasonal discount
     */
    public static PricingRule createSeasonalDiscount(String ruleName, FlightSchedule.Season season, 
                                                    BigDecimal discountPercentage) {
        String ruleId = UUID.randomUUID().toString();
        String description = String.format("Seasonal discount for %s flights", season.getDisplayName());
        RuleConditions conditions = RuleConditions.forSeason(season);
        return new PricingRule(ruleId, ruleName, RuleType.SEASONAL_DISCOUNT, description, 
                              discountPercentage, conditions, 3);
    }
    
    /**
     * Factory method to create a loyalty discount rule.
     * 
     * @param ruleName Rule name
     * @param loyaltyLevel Target loyalty level
     * @param discountPercentage Discount percentage
     * @return PricingRule for loyalty discount
     */
    public static PricingRule createLoyaltyDiscount(String ruleName, CustomerInfo.LoyaltyLevel loyaltyLevel,
                                                   BigDecimal discountPercentage) {
        String ruleId = UUID.randomUUID().toString();
        String description = String.format("Loyalty discount for %s members", loyaltyLevel.getDisplayName());
        RuleConditions conditions = RuleConditions.forLoyaltyLevel(loyaltyLevel);
        return new PricingRule(ruleId, ruleName, RuleType.LOYALTY_DISCOUNT, description,
                              discountPercentage, conditions, 2);
    }
    
    /**
     * Factory method to create an early bird discount rule.
     * 
     * @param ruleName Rule name
     * @param daysInAdvance Minimum days in advance for booking
     * @param discountPercentage Discount percentage
     * @return PricingRule for early bird discount
     */
    public static PricingRule createEarlyBirdDiscount(String ruleName, int daysInAdvance,
                                                     BigDecimal discountPercentage) {
        String ruleId = UUID.randomUUID().toString();
        String description = String.format("Early bird discount for bookings %d+ days in advance", daysInAdvance);
        RuleConditions conditions = RuleConditions.forEarlyBird(daysInAdvance);
        return new PricingRule(ruleId, ruleName, RuleType.EARLY_BIRD_DISCOUNT, description,
                              discountPercentage, conditions, 4);
    }
    
    /**
     * Factory method to create a last-minute deal rule.
     * 
     * @param ruleName Rule name
     * @param hoursBeforeDeparture Maximum hours before departure
     * @param discountPercentage Discount percentage
     * @return PricingRule for last-minute deal
     */
    public static PricingRule createLastMinuteDeal(String ruleName, int hoursBeforeDeparture,
                                                  BigDecimal discountPercentage) {
        String ruleId = UUID.randomUUID().toString();
        String description = String.format("Last-minute deal for bookings within %d hours", hoursBeforeDeparture);
        RuleConditions conditions = RuleConditions.forLastMinute(hoursBeforeDeparture);
        return new PricingRule(ruleId, ruleName, RuleType.LAST_MINUTE_DEAL, description,
                              discountPercentage, conditions, 5);
    }
    
    /**
     * Factory method to create a dynamic pricing rule based on demand.
     * 
     * @param ruleName Rule name
     * @param demandLevel Target demand level
     * @param priceAdjustment Price adjustment (positive for increase, negative for decrease)
     * @return PricingRule for dynamic pricing
     */
    public static PricingRule createDynamicPricing(String ruleName, Flight.DemandLevel demandLevel,
                                                  BigDecimal priceAdjustment) {
        String ruleId = UUID.randomUUID().toString();
        String description = String.format("Dynamic pricing for %s demand flights", demandLevel.getDisplayName());
        RuleConditions conditions = RuleConditions.forDemandLevel(demandLevel);
        return new PricingRule(ruleId, ruleName, RuleType.DYNAMIC_PRICING, description,
                              priceAdjustment, conditions, 1);
    }
    
    /**
     * Evaluates if this rule applies to the given context.
     * 
     * @param flight The flight
     * @param customer The customer
     * @param bookingTime The booking time
     * @return true if rule applies
     */
    public boolean appliesTo(Flight flight, CustomerInfo customer, LocalDateTime bookingTime) {
        if (!isActive) {
            return false;
        }
        
        return conditions.matches(flight, customer, bookingTime);
    }
    
    /**
     * Calculates the price adjustment for this rule.
     * For discounts, this returns the discount amount.
     * For dynamic pricing, this returns the price adjustment.
     * 
     * @param basePrice The base price
     * @return Money representing the price adjustment
     */
    public Money calculatePriceAdjustment(Money basePrice) {
        if (ruleType == RuleType.DYNAMIC_PRICING) {
            // For dynamic pricing, discountPercentage is actually a multiplier
            return basePrice.multiplyByPercentage(discountPercentage);
        } else {
            // For discounts, calculate the discount amount
            return basePrice.multiplyByPercentage(discountPercentage);
        }
    }
    
    /**
     * Applies this rule to a base price.
     * 
     * @param basePrice The base price
     * @return Adjusted price after applying the rule
     */
    public Money applyRule(Money basePrice) {
        Money adjustment = calculatePriceAdjustment(basePrice);
        
        if (ruleType == RuleType.DYNAMIC_PRICING) {
            // Dynamic pricing can increase or decrease price
            if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0) {
                return basePrice.add(adjustment);
            } else {
                return basePrice.subtract(adjustment.multiply(BigDecimal.valueOf(-1)));
            }
        } else {
            // Discount rules always reduce the price
            return basePrice.subtract(adjustment);
        }
    }
    
    /**
     * Activates this pricing rule.
     */
    public void activate() {
        this.isActive = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates this pricing rule.
     */
    public void deactivate() {
        this.isActive = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters
    public String getRuleId() { return ruleId; }
    public String getRuleName() { return ruleName; }
    public RuleType getRuleType() { return ruleType; }
    public String getDescription() { return description; }
    public BigDecimal getDiscountPercentage() { return discountPercentage; }
    public RuleConditions getConditions() { return conditions; }
    public boolean isActive() { return isActive; }
    public int getPriority() { return priority; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    // Validation methods
    private String validateRuleId(String ruleId) {
        Objects.requireNonNull(ruleId, "Rule ID cannot be null");
        if (ruleId.trim().isEmpty()) {
            throw new IllegalArgumentException("Rule ID cannot be empty");
        }
        return ruleId.trim();
    }
    
    private String validateRuleName(String ruleName) {
        Objects.requireNonNull(ruleName, "Rule name cannot be null");
        if (ruleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Rule name cannot be empty");
        }
        return ruleName.trim();
    }
    
    private String validateDescription(String description) {
        Objects.requireNonNull(description, "Description cannot be null");
        return description.trim();
    }
    
    private BigDecimal validateDiscountPercentage(BigDecimal percentage) {
        Objects.requireNonNull(percentage, "Discount percentage cannot be null");
        if (percentage.compareTo(BigDecimal.valueOf(-1)) < 0 || percentage.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("Discount percentage must be between -1.0 and 1.0");
        }
        return percentage;
    }
    
    private int validatePriority(int priority) {
        if (priority < 1 || priority > 10) {
            throw new IllegalArgumentException("Priority must be between 1 and 10");
        }
        return priority;
    }
    
    /**
     * Enumeration for pricing rule types.
     */
    public enum RuleType {
        SEASONAL_DISCOUNT("Seasonal Discount", "Discounts based on travel season"),
        LOYALTY_DISCOUNT("Loyalty Discount", "Discounts for loyalty program members"),
        EARLY_BIRD_DISCOUNT("Early Bird Discount", "Discounts for advance bookings"),
        LAST_MINUTE_DEAL("Last Minute Deal", "Discounts for last-minute bookings"),
        DYNAMIC_PRICING("Dynamic Pricing", "Price adjustments based on demand");
        
        private final String displayName;
        private final String description;
        
        RuleType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PricingRule that = (PricingRule) o;
        return Objects.equals(ruleId, that.ruleId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(ruleId);
    }
    
    @Override
    public String toString() {
        return String.format("PricingRule{%s: %s (%s%% %s)}", 
            ruleName, ruleType.getDisplayName(), 
            discountPercentage.multiply(BigDecimal.valueOf(100)), 
            isActive ? "ACTIVE" : "INACTIVE");
    }
}
