package com.example.ddd.aviation.domain.model;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Flight Entity - Aggregate Root in the Aviation Domain.
 * 
 * Represents a scheduled flight with all its characteristics including route,
 * schedule, capacity, and pricing information. This is the main aggregate root
 * for flight-related operations in the aviation bounded context.
 * 
 * Key Design Decisions:
 * - Aggregate root with strong consistency boundaries
 * - Encapsulates flight business rules and invariants
 * - Uses value objects for complex data (route, schedule, money)
 * - Provides methods for availability and pricing calculations
 * - Maintains seat availability and booking state
 * 
 * <AUTHOR> Aviation System
 */
public class Flight {
    
    private final String flightId;
    private final String flightNumber;
    private final FlightRoute route;
    private final FlightSchedule schedule;
    private final String aircraftType;
    private final int totalCapacity;
    private final Money basePrice;
    private int bookedSeats;
    private FlightStatus status;
    private final LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * Creates a new Flight instance.
     * 
     * @param flightId Unique flight identifier
     * @param flightNumber Flight number (e.g., "AA123")
     * @param route Flight route
     * @param schedule Flight schedule
     * @param aircraftType Aircraft type
     * @param totalCapacity Total seat capacity
     * @param basePrice Base price for the flight
     */
    public Flight(String flightId, String flightNumber, FlightRoute route, 
                  FlightSchedule schedule, String aircraftType, int totalCapacity, Money basePrice) {
        this.flightId = validateFlightId(flightId);
        this.flightNumber = validateFlightNumber(flightNumber);
        this.route = Objects.requireNonNull(route, "Route cannot be null");
        this.schedule = Objects.requireNonNull(schedule, "Schedule cannot be null");
        this.aircraftType = validateAircraftType(aircraftType);
        this.totalCapacity = validateCapacity(totalCapacity);
        this.basePrice = Objects.requireNonNull(basePrice, "Base price cannot be null");
        this.bookedSeats = 0;
        this.status = FlightStatus.SCHEDULED;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Factory method to create a new Flight with generated ID.
     * 
     * @param flightNumber Flight number
     * @param route Flight route
     * @param schedule Flight schedule
     * @param aircraftType Aircraft type
     * @param totalCapacity Total capacity
     * @param basePrice Base price
     * @return New Flight instance
     */
    public static Flight create(String flightNumber, FlightRoute route, FlightSchedule schedule,
                               String aircraftType, int totalCapacity, Money basePrice) {
        String flightId = UUID.randomUUID().toString();
        return new Flight(flightId, flightNumber, route, schedule, aircraftType, totalCapacity, basePrice);
    }
    
    /**
     * Books seats on this flight.
     * 
     * @param seatCount Number of seats to book
     * @throws IllegalArgumentException if not enough seats available
     * @throws IllegalStateException if flight is not bookable
     */
    public void bookSeats(int seatCount) {
        if (seatCount <= 0) {
            throw new IllegalArgumentException("Seat count must be positive");
        }
        
        if (!isBookable()) {
            throw new IllegalStateException("Flight is not available for booking: " + status);
        }
        
        if (getAvailableSeats() < seatCount) {
            throw new IllegalArgumentException(
                String.format("Not enough seats available. Requested: %d, Available: %d", 
                    seatCount, getAvailableSeats()));
        }
        
        this.bookedSeats += seatCount;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Cancels booked seats on this flight.
     * 
     * @param seatCount Number of seats to cancel
     * @throws IllegalArgumentException if trying to cancel more seats than booked
     */
    public void cancelSeats(int seatCount) {
        if (seatCount <= 0) {
            throw new IllegalArgumentException("Seat count must be positive");
        }
        
        if (this.bookedSeats < seatCount) {
            throw new IllegalArgumentException(
                String.format("Cannot cancel more seats than booked. Requested: %d, Booked: %d", 
                    seatCount, this.bookedSeats));
        }
        
        this.bookedSeats -= seatCount;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Gets the number of available seats.
     * 
     * @return Number of available seats
     */
    public int getAvailableSeats() {
        return totalCapacity - bookedSeats;
    }
    
    /**
     * Gets the occupancy rate as a percentage.
     * 
     * @return Occupancy rate (0.0 to 1.0)
     */
    public double getOccupancyRate() {
        return (double) bookedSeats / totalCapacity;
    }
    
    /**
     * Checks if the flight has high demand (>80% occupancy).
     * 
     * @return true if flight has high demand
     */
    public boolean hasHighDemand() {
        return getOccupancyRate() > 0.8;
    }
    
    /**
     * Checks if the flight has low demand (<30% occupancy).
     * 
     * @return true if flight has low demand
     */
    public boolean hasLowDemand() {
        return getOccupancyRate() < 0.3;
    }
    
    /**
     * Gets the demand level for pricing purposes.
     * 
     * @return DemandLevel enum
     */
    public DemandLevel getDemandLevel() {
        double occupancy = getOccupancyRate();
        if (occupancy >= 0.9) return DemandLevel.VERY_HIGH;
        if (occupancy >= 0.7) return DemandLevel.HIGH;
        if (occupancy >= 0.4) return DemandLevel.MEDIUM;
        if (occupancy >= 0.2) return DemandLevel.LOW;
        return DemandLevel.VERY_LOW;
    }
    
    /**
     * Checks if the flight is bookable.
     * 
     * @return true if flight can accept bookings
     */
    public boolean isBookable() {
        return status == FlightStatus.SCHEDULED && 
               getAvailableSeats() > 0 &&
               !schedule.departureTime().isBefore(LocalDateTime.now());
    }
    
    /**
     * Updates the flight status.
     * 
     * @param newStatus New flight status
     */
    public void updateStatus(FlightStatus newStatus) {
        Objects.requireNonNull(newStatus, "Status cannot be null");
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Checks if this is a domestic flight.
     * 
     * @return true if domestic
     */
    public boolean isDomestic() {
        return route.isDomestic();
    }
    
    /**
     * Checks if this is an international flight.
     * 
     * @return true if international
     */
    public boolean isInternational() {
        return route.isInternational();
    }
    
    // Getters
    public String getFlightId() { return flightId; }
    public String getFlightNumber() { return flightNumber; }
    public FlightRoute getRoute() { return route; }
    public FlightSchedule getSchedule() { return schedule; }
    public String getAircraftType() { return aircraftType; }
    public int getTotalCapacity() { return totalCapacity; }
    public Money getBasePrice() { return basePrice; }
    public int getBookedSeats() { return bookedSeats; }
    public FlightStatus getStatus() { return status; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    // Validation methods
    private String validateFlightId(String flightId) {
        Objects.requireNonNull(flightId, "Flight ID cannot be null");
        if (flightId.trim().isEmpty()) {
            throw new IllegalArgumentException("Flight ID cannot be empty");
        }
        return flightId.trim();
    }
    
    private String validateFlightNumber(String flightNumber) {
        Objects.requireNonNull(flightNumber, "Flight number cannot be null");
        String trimmed = flightNumber.trim().toUpperCase();
        if (trimmed.isEmpty()) {
            throw new IllegalArgumentException("Flight number cannot be empty");
        }
        if (!trimmed.matches("[A-Z]{2}\\d{1,4}")) {
            throw new IllegalArgumentException("Flight number must be in format: AA123 (2 letters + 1-4 digits)");
        }
        return trimmed;
    }
    
    private String validateAircraftType(String aircraftType) {
        Objects.requireNonNull(aircraftType, "Aircraft type cannot be null");
        if (aircraftType.trim().isEmpty()) {
            throw new IllegalArgumentException("Aircraft type cannot be empty");
        }
        return aircraftType.trim();
    }
    
    private int validateCapacity(int capacity) {
        if (capacity <= 0) {
            throw new IllegalArgumentException("Capacity must be positive");
        }
        if (capacity > 1000) {
            throw new IllegalArgumentException("Capacity cannot exceed 1000 seats");
        }
        return capacity;
    }
    
    /**
     * Enumeration for flight status.
     */
    public enum FlightStatus {
        SCHEDULED("Scheduled", "Flight is scheduled and bookable"),
        BOARDING("Boarding", "Passengers are boarding"),
        DEPARTED("Departed", "Flight has departed"),
        ARRIVED("Arrived", "Flight has arrived"),
        CANCELLED("Cancelled", "Flight has been cancelled"),
        DELAYED("Delayed", "Flight is delayed but still scheduled");
        
        private final String displayName;
        private final String description;
        
        FlightStatus(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * Enumeration for demand levels.
     */
    public enum DemandLevel {
        VERY_LOW("Very Low", 0.0, "Less than 20% occupancy"),
        LOW("Low", 0.05, "20-40% occupancy"),
        MEDIUM("Medium", 0.10, "40-70% occupancy"),
        HIGH("High", 0.20, "70-90% occupancy"),
        VERY_HIGH("Very High", 0.35, "Over 90% occupancy");
        
        private final String displayName;
        private final double priceMultiplier;
        private final String description;
        
        DemandLevel(String displayName, double priceMultiplier, String description) {
            this.displayName = displayName;
            this.priceMultiplier = priceMultiplier;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public double getPriceMultiplier() { return priceMultiplier; }
        public String getDescription() { return description; }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Flight flight = (Flight) o;
        return Objects.equals(flightId, flight.flightId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(flightId);
    }
    
    @Override
    public String toString() {
        return String.format("Flight{%s: %s, %s, %s, %d/%d seats}", 
            flightNumber, route, schedule.departureTime(), status.getDisplayName(), 
            bookedSeats, totalCapacity);
    }
}
