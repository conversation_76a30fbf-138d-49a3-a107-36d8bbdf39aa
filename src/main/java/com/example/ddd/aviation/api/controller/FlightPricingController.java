package com.example.ddd.aviation.api.controller;

import com.example.ddd.aviation.application.dto.*;
import com.example.ddd.aviation.application.service.TicketPricingApplicationService;
import com.example.ddd.aviation.domain.model.Money;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * REST Controller for flight pricing operations.
 * 
 * This controller provides endpoints for calculating ticket prices,
 * searching flights, and retrieving pricing information. It follows
 * REST principles and provides comprehensive API documentation.
 * 
 * Key Design Decisions:
 * - RESTful endpoint design with proper HTTP methods
 * - Comprehensive OpenAPI documentation
 * - Input validation using Bean Validation
 * - Proper HTTP status codes and error handling
 * - Clear separation between API and application layers
 * 
 * <AUTHOR> Aviation System
 */
@RestController
@RequestMapping("/api/flights")
@Tag(name = "Flight Pricing", description = "Operations for flight pricing and booking")
public class FlightPricingController {
    
    private final TicketPricingApplicationService pricingService;
    
    public FlightPricingController(TicketPricingApplicationService pricingService) {
        this.pricingService = pricingService;
    }
    
    /**
     * Calculate ticket price for a specific flight.
     */
    @GetMapping("/{flightId}/pricing")
    @Operation(
        summary = "Calculate ticket price for a flight",
        description = "Calculates the ticket price for a specific flight based on customer information and booking context"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Price calculated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
        @ApiResponse(responseCode = "404", description = "Flight not found"),
        @ApiResponse(responseCode = "422", description = "Flight not bookable")
    })
    public ResponseEntity<PricingResponseDTO> calculateFlightPrice(
            @Parameter(description = "Flight ID", required = true)
            @PathVariable String flightId,
            
            @Parameter(description = "Customer ID", required = true)
            @RequestParam String customerId,
            
            @Parameter(description = "Customer first name", required = true)
            @RequestParam String firstName,
            
            @Parameter(description = "Customer last name", required = true)
            @RequestParam String lastName,
            
            @Parameter(description = "Customer email", required = true)
            @RequestParam String email,
            
            @Parameter(description = "Customer loyalty level", required = true)
            @RequestParam String loyaltyLevel,
            
            @Parameter(description = "Customer total flights", required = false)
            @RequestParam(defaultValue = "0") Integer totalFlights,
            
            @Parameter(description = "Number of passengers", required = false)
            @RequestParam(defaultValue = "1") Integer numberOfPassengers) {
        
        PricingRequestDTO request = new PricingRequestDTO(
            flightId, customerId, firstName, lastName, email, loyaltyLevel,
            totalFlights, null, null, numberOfPassengers
        );
        
        PricingResponseDTO response = pricingService.calculateTicketPrice(request);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Calculate detailed ticket price with full request body.
     */
    @PostMapping("/price-calculation")
    @Operation(
        summary = "Calculate detailed ticket price",
        description = "Calculates ticket price with detailed customer information and booking preferences"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Price calculated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request body"),
        @ApiResponse(responseCode = "404", description = "Flight not found"),
        @ApiResponse(responseCode = "422", description = "Flight not bookable")
    })
    public ResponseEntity<PricingResponseDTO> calculateDetailedPrice(
            @Parameter(description = "Pricing calculation request", required = true)
            @Valid @RequestBody PricingRequestDTO request) {
        
        PricingResponseDTO response = pricingService.calculateTicketPrice(request);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get quick price estimate for a flight.
     */
    @GetMapping("/{flightId}/price-estimate")
    @Operation(
        summary = "Get quick price estimate",
        description = "Gets a quick price estimate for a flight without detailed rule evaluation"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Estimate calculated successfully"),
        @ApiResponse(responseCode = "404", description = "Flight not found")
    })
    public ResponseEntity<QuickPriceEstimateDTO> getQuickPriceEstimate(
            @Parameter(description = "Flight ID", required = true)
            @PathVariable String flightId,
            
            @Parameter(description = "Customer loyalty level", required = false)
            @RequestParam(defaultValue = "BRONZE") String loyaltyLevel) {
        
        Money estimate = pricingService.getQuickPriceEstimate(flightId, loyaltyLevel);
        
        QuickPriceEstimateDTO response = new QuickPriceEstimateDTO(
            flightId, estimate.amount(), estimate.currency().getCurrencyCode(), loyaltyLevel
        );
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * Search available flights.
     */
    @GetMapping("/search")
    @Operation(
        summary = "Search available flights",
        description = "Searches for available flights based on route and date criteria"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Flights found successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    })
    public ResponseEntity<List<FlightDTO>> searchFlights(
            @Parameter(description = "Origin airport code")
            @RequestParam(required = false) String origin,
            
            @Parameter(description = "Destination airport code")
            @RequestParam(required = false) String destination,
            
            @Parameter(description = "Departure date (YYYY-MM-DD)")
            @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate departureDate) {
        
        List<FlightDTO> flights;
        
        if (origin != null && destination != null && departureDate != null) {
            flights = pricingService.findAvailableFlights(origin, destination, departureDate);
        } else {
            flights = pricingService.getAllAvailableFlights();
        }
        
        return ResponseEntity.ok(flights);
    }
    
    /**
     * Get flight details by ID.
     */
    @GetMapping("/{flightId}")
    @Operation(
        summary = "Get flight details",
        description = "Retrieves detailed information about a specific flight"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Flight found successfully"),
        @ApiResponse(responseCode = "404", description = "Flight not found")
    })
    public ResponseEntity<FlightDTO> getFlightById(
            @Parameter(description = "Flight ID", required = true)
            @PathVariable String flightId) {
        
        Optional<FlightDTO> flight = pricingService.getFlightById(flightId);
        
        return flight.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get applicable pricing rules for a flight and customer.
     */
    @PostMapping("/{flightId}/applicable-rules")
    @Operation(
        summary = "Get applicable pricing rules",
        description = "Gets all pricing rules that apply to a specific flight and customer combination"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Rules retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request body"),
        @ApiResponse(responseCode = "404", description = "Flight not found")
    })
    public ResponseEntity<List<PricingRuleDTO>> getApplicablePricingRules(
            @Parameter(description = "Flight ID", required = true)
            @PathVariable String flightId,
            
            @Parameter(description = "Customer information", required = true)
            @Valid @RequestBody PricingRequestDTO request) {
        
        List<PricingRuleDTO> rules = pricingService.getApplicablePricingRules(flightId, request);
        return ResponseEntity.ok(rules);
    }
    
    /**
     * Get pricing statistics for a flight.
     */
    @GetMapping("/{flightId}/pricing-stats")
    @Operation(
        summary = "Get flight pricing statistics",
        description = "Gets pricing statistics and discount availability for a specific flight"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Flight not found")
    })
    public ResponseEntity<FlightPricingStatsDTO> getFlightPricingStats(
            @Parameter(description = "Flight ID", required = true)
            @PathVariable String flightId) {
        
        FlightPricingStatsDTO stats = pricingService.getFlightPricingStats(flightId);
        return ResponseEntity.ok(stats);
    }
    
    /**
     * Simple DTO for quick price estimates.
     */
    public record QuickPriceEstimateDTO(
        String flightId,
        java.math.BigDecimal estimatedPrice,
        String currency,
        String loyaltyLevel
    ) {}
}
