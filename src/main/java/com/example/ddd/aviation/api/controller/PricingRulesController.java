package com.example.ddd.aviation.api.controller;

import com.example.ddd.aviation.application.dto.*;
import com.example.ddd.aviation.application.service.TicketPricingApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * REST Controller for pricing rules management.
 * 
 * This controller provides endpoints for managing pricing rules,
 * including retrieval, creation, and rule information queries.
 * It supports both business users and system administrators.
 * 
 * Key Design Decisions:
 * - RESTful endpoint design for rule management
 * - Comprehensive OpenAPI documentation
 * - Input validation for rule creation
 * - Proper HTTP status codes and error handling
 * - Support for different rule types and filtering
 * 
 * <AUTHOR> Aviation System
 */
@RestController
@RequestMapping("/api/pricing-rules")
@Tag(name = "Pricing Rules", description = "Operations for managing pricing rules")
public class PricingRulesController {
    
    private final TicketPricingApplicationService pricingService;
    
    public PricingRulesController(TicketPricingApplicationService pricingService) {
        this.pricingService = pricingService;
    }
    
    /**
     * Get all active pricing rules.
     */
    @GetMapping
    @Operation(
        summary = "Get all active pricing rules",
        description = "Retrieves all currently active pricing rules in the system"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Rules retrieved successfully")
    })
    public ResponseEntity<List<PricingRuleDTO>> getAllActivePricingRules() {
        List<PricingRuleDTO> rules = pricingService.getActivePricingRules();
        return ResponseEntity.ok(rules);
    }
    
    /**
     * Get pricing rules by type.
     */
    @GetMapping("/by-type/{ruleType}")
    @Operation(
        summary = "Get pricing rules by type",
        description = "Retrieves all active pricing rules of a specific type"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Rules retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid rule type")
    })
    public ResponseEntity<List<PricingRuleDTO>> getPricingRulesByType(
            @Parameter(
                description = "Rule type (SEASONAL_DISCOUNT, LOYALTY_DISCOUNT, EARLY_BIRD_DISCOUNT, LAST_MINUTE_DEAL, DYNAMIC_PRICING)",
                required = true
            )
            @PathVariable String ruleType) {
        
        try {
            List<PricingRuleDTO> rules = pricingService.getPricingRulesByType(ruleType);
            return ResponseEntity.ok(rules);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Create a new pricing rule.
     */
    @PostMapping
    @Operation(
        summary = "Create a new pricing rule",
        description = "Creates a new pricing rule in the system (admin operation)"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "201", description = "Rule created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid rule configuration"),
        @ApiResponse(responseCode = "409", description = "Rule with same name already exists")
    })
    public ResponseEntity<PricingRuleDTO> createPricingRule(
            @Parameter(description = "Pricing rule creation request", required = true)
            @Valid @RequestBody CreatePricingRuleDTO request) {
        
        // Validate rule configuration
        if (!request.isValidConfiguration()) {
            return ResponseEntity.badRequest().build();
        }
        
        try {
            PricingRuleDTO createdRule = pricingService.createPricingRule(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdRule);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get system pricing statistics.
     */
    @GetMapping("/stats")
    @Operation(
        summary = "Get system pricing statistics",
        description = "Retrieves overall statistics about the pricing system"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    })
    public ResponseEntity<SystemPricingStatsDTO> getSystemPricingStats() {
        SystemPricingStatsDTO stats = pricingService.getSystemPricingStats();
        return ResponseEntity.ok(stats);
    }
    
    /**
     * Get rule type information.
     */
    @GetMapping("/rule-types")
    @Operation(
        summary = "Get available rule types",
        description = "Retrieves information about all available pricing rule types"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Rule types retrieved successfully")
    })
    public ResponseEntity<List<RuleTypeInfoDTO>> getRuleTypes() {
        List<RuleTypeInfoDTO> ruleTypes = List.of(
            new RuleTypeInfoDTO("SEASONAL_DISCOUNT", "Seasonal Discount", 
                "Discounts based on travel season", "targetSeason"),
            new RuleTypeInfoDTO("LOYALTY_DISCOUNT", "Loyalty Discount", 
                "Discounts for loyalty program members", "minimumLoyaltyLevel"),
            new RuleTypeInfoDTO("EARLY_BIRD_DISCOUNT", "Early Bird Discount", 
                "Discounts for advance bookings", "minimumDaysInAdvance"),
            new RuleTypeInfoDTO("LAST_MINUTE_DEAL", "Last Minute Deal", 
                "Discounts for last-minute bookings", "maximumHoursBeforeDeparture"),
            new RuleTypeInfoDTO("DYNAMIC_PRICING", "Dynamic Pricing", 
                "Price adjustments based on demand", "targetDemandLevel")
        );
        
        return ResponseEntity.ok(ruleTypes);
    }
    
    /**
     * Get loyalty levels information.
     */
    @GetMapping("/loyalty-levels")
    @Operation(
        summary = "Get available loyalty levels",
        description = "Retrieves information about all available customer loyalty levels"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Loyalty levels retrieved successfully")
    })
    public ResponseEntity<List<LoyaltyLevelInfoDTO>> getLoyaltyLevels() {
        List<LoyaltyLevelInfoDTO> loyaltyLevels = List.of(
            new LoyaltyLevelInfoDTO("BRONZE", "Bronze", "Entry level", 0.0, 0),
            new LoyaltyLevelInfoDTO("SILVER", "Silver", "Frequent traveler", 0.05, 10),
            new LoyaltyLevelInfoDTO("GOLD", "Gold", "Premium member", 0.10, 25),
            new LoyaltyLevelInfoDTO("PLATINUM", "Platinum", "Elite member", 0.15, 50),
            new LoyaltyLevelInfoDTO("DIAMOND", "Diamond", "Top tier", 0.20, 100)
        );
        
        return ResponseEntity.ok(loyaltyLevels);
    }
    
    /**
     * Get seasons information.
     */
    @GetMapping("/seasons")
    @Operation(
        summary = "Get available seasons",
        description = "Retrieves information about all available seasons for seasonal rules"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Seasons retrieved successfully")
    })
    public ResponseEntity<List<SeasonInfoDTO>> getSeasons() {
        List<SeasonInfoDTO> seasons = List.of(
            new SeasonInfoDTO("SPRING", "Spring", "March - May"),
            new SeasonInfoDTO("SUMMER", "Summer", "June - August"),
            new SeasonInfoDTO("FALL", "Fall", "September - November"),
            new SeasonInfoDTO("WINTER", "Winter", "December - February")
        );
        
        return ResponseEntity.ok(seasons);
    }
    
    /**
     * Get demand levels information.
     */
    @GetMapping("/demand-levels")
    @Operation(
        summary = "Get available demand levels",
        description = "Retrieves information about all available demand levels for dynamic pricing"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Demand levels retrieved successfully")
    })
    public ResponseEntity<List<DemandLevelInfoDTO>> getDemandLevels() {
        List<DemandLevelInfoDTO> demandLevels = List.of(
            new DemandLevelInfoDTO("VERY_LOW", "Very Low", "Less than 20% occupancy", 0.0),
            new DemandLevelInfoDTO("LOW", "Low", "20-40% occupancy", 0.05),
            new DemandLevelInfoDTO("MEDIUM", "Medium", "40-70% occupancy", 0.10),
            new DemandLevelInfoDTO("HIGH", "High", "70-90% occupancy", 0.20),
            new DemandLevelInfoDTO("VERY_HIGH", "Very High", "Over 90% occupancy", 0.35)
        );
        
        return ResponseEntity.ok(demandLevels);
    }
    
    // Supporting DTOs for metadata endpoints
    
    public record RuleTypeInfoDTO(
        String code,
        String displayName,
        String description,
        String requiredCondition
    ) {}
    
    public record LoyaltyLevelInfoDTO(
        String code,
        String displayName,
        String description,
        Double discountPercentage,
        Integer minimumFlights
    ) {}
    
    public record SeasonInfoDTO(
        String code,
        String displayName,
        String months
    ) {}
    
    public record DemandLevelInfoDTO(
        String code,
        String displayName,
        String description,
        Double priceMultiplier
    ) {}
}
