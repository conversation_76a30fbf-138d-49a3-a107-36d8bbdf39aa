package com.example.ddd.aviation.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for the Aviation API.
 * 
 * This handler provides centralized exception handling across all controllers,
 * ensuring consistent error responses and proper HTTP status codes. It handles
 * both business exceptions and technical validation errors.
 * 
 * Key Design Decisions:
 * - Centralized error handling for consistency
 * - Structured error responses with detailed information
 * - Proper HTTP status codes for different error types
 * - Security-conscious error messages (no sensitive data exposure)
 * - Comprehensive logging for debugging
 * 
 * <AUTHOR> Aviation System
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * Handles validation errors from @Valid annotations.
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationErrors(MethodArgumentNotValidException ex) {
        Map<String, String> fieldErrors = new HashMap<>();
        
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            fieldErrors.put(fieldName, errorMessage);
        });
        
        ErrorResponse errorResponse = new ErrorResponse(
            "VALIDATION_ERROR",
            "Request validation failed",
            "One or more fields have invalid values",
            LocalDateTime.now(),
            fieldErrors
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }
    
    /**
     * Handles illegal argument exceptions (business rule violations).
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
        ErrorResponse errorResponse = new ErrorResponse(
            "INVALID_REQUEST",
            "Invalid request parameter",
            ex.getMessage(),
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }
    
    /**
     * Handles illegal state exceptions (business state violations).
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ErrorResponse> handleIllegalStateException(IllegalStateException ex) {
        ErrorResponse errorResponse = new ErrorResponse(
            "INVALID_STATE",
            "Invalid operation state",
            ex.getMessage(),
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(errorResponse);
    }
    
    /**
     * Handles method argument type mismatch (e.g., invalid enum values).
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponse> handleTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        String message = String.format("Invalid value '%s' for parameter '%s'. Expected type: %s", 
            ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "TYPE_MISMATCH",
            "Invalid parameter type",
            message,
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }
    
    /**
     * Handles flight not found exceptions.
     */
    @ExceptionHandler(FlightNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleFlightNotFoundException(FlightNotFoundException ex) {
        ErrorResponse errorResponse = new ErrorResponse(
            "FLIGHT_NOT_FOUND",
            "Flight not found",
            ex.getMessage(),
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.notFound().build();
    }
    
    /**
     * Handles flight not bookable exceptions.
     */
    @ExceptionHandler(FlightNotBookableException.class)
    public ResponseEntity<ErrorResponse> handleFlightNotBookableException(FlightNotBookableException ex) {
        ErrorResponse errorResponse = new ErrorResponse(
            "FLIGHT_NOT_BOOKABLE",
            "Flight not available for booking",
            ex.getMessage(),
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(errorResponse);
    }
    
    /**
     * Handles pricing calculation exceptions.
     */
    @ExceptionHandler(PricingCalculationException.class)
    public ResponseEntity<ErrorResponse> handlePricingCalculationException(PricingCalculationException ex) {
        ErrorResponse errorResponse = new ErrorResponse(
            "PRICING_CALCULATION_ERROR",
            "Error calculating ticket price",
            ex.getMessage(),
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * Handles all other unexpected exceptions.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        // Log the full exception for debugging (in production, use proper logging)
        System.err.println("Unexpected error: " + ex.getMessage());
        ex.printStackTrace();
        
        ErrorResponse errorResponse = new ErrorResponse(
            "INTERNAL_ERROR",
            "An unexpected error occurred",
            "Please try again later or contact support if the problem persists",
            LocalDateTime.now(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * Structured error response DTO.
     */
    public record ErrorResponse(
        String errorCode,
        String errorMessage,
        String errorDetails,
        LocalDateTime timestamp,
        Map<String, String> fieldErrors
    ) {
        
        /**
         * Creates a simple error response without field errors.
         */
        public static ErrorResponse simple(String code, String message, String details) {
            return new ErrorResponse(code, message, details, LocalDateTime.now(), null);
        }
        
        /**
         * Creates an error response with field validation errors.
         */
        public static ErrorResponse withFieldErrors(String code, String message, String details, 
                                                   Map<String, String> fieldErrors) {
            return new ErrorResponse(code, message, details, LocalDateTime.now(), fieldErrors);
        }
    }
    
    // Custom exception classes for specific business scenarios
    
    /**
     * Exception thrown when a flight is not found.
     */
    public static class FlightNotFoundException extends RuntimeException {
        public FlightNotFoundException(String flightId) {
            super("Flight not found with ID: " + flightId);
        }
    }
    
    /**
     * Exception thrown when a flight is not available for booking.
     */
    public static class FlightNotBookableException extends RuntimeException {
        public FlightNotBookableException(String flightId, String reason) {
            super("Flight " + flightId + " is not available for booking: " + reason);
        }
    }
    
    /**
     * Exception thrown when pricing calculation fails.
     */
    public static class PricingCalculationException extends RuntimeException {
        public PricingCalculationException(String message, Throwable cause) {
            super("Pricing calculation failed: " + message, cause);
        }
    }
}
