package com.example.ddd.aviation.api.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.List;

/**
 * OpenAPI configuration for the Aviation Pricing API.
 * 
 * This configuration sets up comprehensive API documentation using OpenAPI 3.0
 * (Swagger). It provides detailed information about the API, including endpoints,
 * request/response schemas, and example usage.
 * 
 * Key Design Decisions:
 * - Comprehensive API documentation for developers
 * - Clear examples and descriptions for all endpoints
 * - Proper categorization of endpoints by functionality
 * - Contact information and licensing details
 * - Server configuration for different environments
 * 
 * <AUTHOR> Aviation System
 */
@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI aviationPricingOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Aviation Flexible Ticket Pricing API")
                .description("""
                    A comprehensive API for civil aviation flexible ticket pricing system built with Domain-Driven Design principles.
                    
                    This API provides endpoints for:
                    - Flight search and information retrieval
                    - Dynamic ticket price calculations
                    - Pricing rule management
                    - Customer loyalty program integration
                    - Real-time demand-based pricing
                    
                    ## Key Features
                    
                    ### Pricing Rules
                    - **Seasonal Discounts**: Automatic price adjustments based on travel seasons
                    - **Loyalty Discounts**: Tiered discounts for frequent flyers (Bronze, Silver, Gold, Platinum, Diamond)
                    - **Early Bird Specials**: Discounts for advance bookings (30+ or 90+ days)
                    - **Last-Minute Deals**: Special pricing for bookings within 24 hours
                    - **Dynamic Pricing**: Real-time price adjustments based on seat availability and demand
                    
                    ### Customer Loyalty Levels
                    - **Bronze**: Entry level (0% discount)
                    - **Silver**: 5% discount for frequent travelers
                    - **Gold**: 10% discount for premium members
                    - **Platinum**: 15% discount for elite members
                    - **Diamond**: 20% discount for top-tier customers
                    
                    ### Business Rules
                    - Prices are calculated in real-time based on multiple factors
                    - Rules are applied in priority order with conflict resolution
                    - Maximum discount limits prevent extreme price reductions
                    - Dynamic pricing responds to seat availability and booking patterns
                    
                    ## Usage Examples
                    
                    ### Calculate Ticket Price
                    ```
                    GET /api/flights/{flightId}/pricing?customerId=CUST123&loyaltyLevel=GOLD
                    ```
                    
                    ### Search Available Flights
                    ```
                    GET /api/flights/search?origin=LAX&destination=JFK&departureDate=2024-06-15
                    ```
                    
                    ### Get Pricing Rules
                    ```
                    GET /api/pricing-rules/by-type/LOYALTY_DISCOUNT
                    ```
                    """)
                .version("1.0.0")
                .contact(new Contact()
                    .name("Aviation Pricing Team")
                    .email("<EMAIL>")
                    .url("https://aviation-example.com/support"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT")))
            .servers(List.of(
                new Server()
                    .url("http://localhost:8080")
                    .description("Development server"),
                new Server()
                    .url("https://api-staging.aviation-example.com")
                    .description("Staging server"),
                new Server()
                    .url("https://api.aviation-example.com")
                    .description("Production server")
            ));
    }
}
